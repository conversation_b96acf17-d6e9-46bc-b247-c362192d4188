package com.yanfuinvest.operation.controller

import com.yanfuinvest.common.domain.http.CustomResponse
import com.yanfuinvest.operation.domain.dto.SearchSTStockRequestDTO
import com.yanfuinvest.operation.domain.dto.SearchStStockErrorResultDTO
import com.yanfuinvest.operation.domain.dto.SearchStStockResDTO
import com.yanfuinvest.operation.domain.dto.SearchStStockResultDTO
import com.yanfuinvest.operation.service.StStockService
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@Validated
@RequestMapping("/operation/portfolio/StStockMonitoring")
//@PreAuthorize("hasPermission('gte', @perms.portfolioOperation)")
class StStockMonitoringController(
    private val service: StStockService
) {
    @PostMapping("")
    fun sendRequestData(@RequestBody data: SearchSTStockRequestDTO): CustomResponse<List<SearchStStockResultDTO>?> {
        return CustomResponse.Ok(service.sendRequest(data))
    }

    @PostMapping("/valuationDate")
    fun sendValuationDate(@RequestBody data:String):CustomResponse<Boolean?>{
        return CustomResponse.Ok(service.sendValuationDateRequest(data))
    }

    @GetMapping("/statusMonitor")
    fun statusMonitoring():CustomResponse<SearchStStockResDTO>{
        return CustomResponse.Ok(service.statusMonitor())
    }

}