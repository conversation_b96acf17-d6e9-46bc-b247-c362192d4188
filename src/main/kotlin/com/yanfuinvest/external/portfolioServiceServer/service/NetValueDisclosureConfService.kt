package com.yanfuinvest.external.portfolioServiceServer.service

import com.yanfuinvest.common.domain.PaginationRequest
import com.yanfuinvest.common.extension.rest.execute
import com.yanfuinvest.external.portfolioServiceServer.domain.NetValueDisclosureConf
import com.yanfuinvest.external.portfolioServiceServer.domain.PortfolioServiceServerResponseList
import com.yanfuinvest.external.portfolioServiceServer.utils.PortfolioServiceServerUtils
import org.springframework.core.ParameterizedTypeReference
import org.springframework.http.HttpMethod
import org.springframework.stereotype.Service


@Service
class NetValueDisclosureConfService(
    private val prefix: String = "netValueDisclosureConf"
): PortfolioServiceServerServiceBase<Any>() {
    override val getListPath = "$prefix.list"
    private val url get() = "$serverUrl/trpc/$prefix"

    fun getList(
        paginationRequest: PaginationRequest,
        portfolioId: Long?,
        portfolioState: String?,
        status: String?,
        disclosePeriod: List<String>?,
        portfolioPublishDate: List<String>?,
    ): Pair<List<Any>, Long> {
        val paginationInput = PortfolioServiceServerUtils.parsePagination(paginationRequest)
        val filterInput = mapOf(
            "portfolioId" to portfolioId,
            "portfolioState" to portfolioState,
            "status" to status,
            "disclosePeriod" to disclosePeriod,
            "portfolioPublishDate" to portfolioPublishDate,
        )
        val input = paginationInput.plus(filterInput)

        return getList(input)
    }

    fun getDisplayList(
        portfolioId: Long?,
        portfolioState: String?,
        status: String?,
        disclosePeriod: List<String>?,
        portfolioPublishDate: List<String>?,
    ): List<NetValueDisclosureConf> {
        val input = mapOf(
            "portfolioId" to portfolioId,
            "portfolioState" to portfolioState,
            "status" to status,
            "disclosePeriod" to disclosePeriod,
            "portfolioPublishDate" to portfolioPublishDate,
        )

        val listResponseType = object : ParameterizedTypeReference<PortfolioServiceServerResponseList<NetValueDisclosureConf>>() {}
        val result = request("$prefix.displayList", HttpMethod.GET, input, listResponseType)
        return result?.result?.data?.items ?: emptyList()
    }

    fun update(data: Any) {
        restTemplate.execute { postForEntity("${url}.update", getRequestEntity(data), Any::class.java) }
    }

    fun delete(portfolioId: Long) {
        val entity = getRequestEntity(mapOf("portfolioId" to portfolioId))
        restTemplate.execute { postForEntity("${url}.delete", entity, Any::class.java) }
    }
}