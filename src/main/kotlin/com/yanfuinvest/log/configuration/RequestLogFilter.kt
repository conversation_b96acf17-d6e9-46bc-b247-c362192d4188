package com.yanfuinvest.log.configuration

import com.yanfuinvest.account.domain.User
import com.yanfuinvest.log.domain.RequestLog
import com.yanfuinvest.log.repository.RequestLogRepository
import org.apache.commons.io.IOUtils
import org.springframework.core.annotation.Order
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Component
import org.springframework.web.filter.OncePerRequestFilter
import org.springframework.web.util.ContentCachingRequestWrapper
import org.springframework.web.util.WebUtils
import java.io.IOException
import java.sql.Timestamp
import java.time.Duration
import java.time.LocalDateTime
import javax.servlet.FilterChain
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse

@Component
@Order(1)
class RequestLogFilter(private val rRepo: RequestLogRepository): OncePerRequestFilter() {
    override fun doFilterInternal(request: HttpServletRequest, response: HttpServletResponse, chain: FilterChain) {
        if (request.method == "GET" ||
            request.contentType == null ||
            request.contentType.contains(MediaType.MULTIPART_FORM_DATA_VALUE)
        ) {
            chain.doFilter(request, response)
            return
        }

        val req = ContentCachingRequestWrapper(request)

        val startTime = LocalDateTime.now()
        chain.doFilter(req, response)
        val endTime = LocalDateTime.now()

        val user = SecurityContextHolder.getContext().authentication?.principal
        if (user == null || user !is User) return

        val datetime = Timestamp.valueOf(startTime)

        val userId = user.id

        val body = getRequestBody(req)
        val method = req.method
        val path = req.requestURI

        val refererSplit = req.getHeader("referer")?.split("/")
        val page = refererSplit?.slice(3 until refererSplit.size)?.joinToString("/")

        val params = req.parameterMap
        val code = response.status

        val duration = Duration.between(startTime, endTime)
        val timecosts = duration.toNanos().toDouble() / 1000000000

        val useragent = req.getHeader("user-agent")

        val requestLog = RequestLog(datetime, userId, method, path, page, params, code, timecosts, useragent, body)
        rRepo.save(requestLog)
    }

    private fun getRequestBody(request: HttpServletRequest): String? {
        var body: String? = null

        val wrapper = WebUtils.getNativeRequest(request, ContentCachingRequestWrapper::class.java)
        wrapper?.let {
            try {
                body = IOUtils.toString(wrapper.contentAsByteArray, wrapper.characterEncoding)
            } catch (_: IOException) {}
        }

        return body
    }
}
