package com.yanfuinvest.common.domain

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonProperty
import com.yanfuinvest.account.domain.User
import org.springframework.data.annotation.CreatedBy
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedBy
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.sql.Timestamp
import javax.persistence.*

@MappedSuperclass
@EntityListeners(AuditingEntityListener::class)
abstract class EntityBase {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null

    @CreatedDate
    @Column(updatable = false)
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    var createTime: Timestamp? = null

    @CreatedBy
    @JsonIgnore
    @ManyToOne()
    var createUser: User? = null

    @LastModifiedDate
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    var updateTime: Timestamp? = null

    @LastModifiedBy
    @JsonIgnore
    @ManyToOne()
    var updateUser: User? = null

    val createUserId
        get() = createUser?.id

    val updateUserId
        get() = updateUser?.id
}
