package com.yanfuinvest.common.domain

import com.yanfuinvest.common.annotation.NoArg
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort

@NoArg
class PaginationRequest(
    val page: Int,
    val pageSize: Int,
    val sortKey: String? = null,
    val sortDesc: Boolean? = null
) {
    fun toPageRequest(): PageRequest {
        if (sortKey == null) return PageRequest.of(page, pageSize)

        val direction = if (sortDesc == true) Sort.Direction.DESC else Sort.Direction.ASC
        return PageRequest.of(page, pageSize, direction, sortKey)
    }

    val limit
        get() = pageSize.toLong()

    val offset
        get() = (page * pageSize).toLong()
}

