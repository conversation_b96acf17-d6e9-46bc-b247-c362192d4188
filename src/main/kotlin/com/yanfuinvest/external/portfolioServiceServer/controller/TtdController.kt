package com.yanfuinvest.external.portfolioServiceServer.controller

import com.yanfuinvest.common.domain.http.CustomResponse
import com.yanfuinvest.external.portfolioServiceServer.service.TtdService
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@Validated
@RestController
@RequestMapping("/external/portfolio_service_server/ttd")
class TtdController(private val service: TtdService) {
    @PostMapping("product_list")
    fun getProductList(@RequestBody(required = false) input: Map<String, Any?>?) =
        CustomResponse.Ok(service.getProductList(input))
}