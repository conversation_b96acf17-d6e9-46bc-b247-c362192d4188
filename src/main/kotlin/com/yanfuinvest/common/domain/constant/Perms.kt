package com.yanfuinvest.common.domain.constant

import com.fasterxml.jackson.annotation.JsonIgnore
import org.springframework.stereotype.Component

/**
 * 所有权限常量。
 * 因为权限直接跟功能关联，所以不存储在数据库里，而是写在这里
 */
@Component
final class Perms (
) {
    class PermDetail (
        val name: String,
        val description: String,
        @JsonIgnore
        val parent: PermDetail?
    )

    val readonlyUser = PermDetail(
        "readonlyUser",
        "只读用户",
        null
    )

    val superAdmin = PermDetail(
        "superAdmin",
        "系统管理员",
        null
    )

    val accountAdmin = PermDetail(
        "accountAdmin",
        "账号管理员，可管理账号、角色、权限等",
        superAdmin
    )

    val accountDetail = PermDetail(
        "accountDetail",
        "账号详情录入",
        accountAdmin
    )

    val investorMaintainer = PermDetail(
        "investorMaintainer",
        "投资者材料维护",
        superAdmin
    )

    val investorReview = PermDetail(
        "investorReview",
        "投资者材料审核",
        superAdmin
    )

    val tradeSponsor = PermDetail(
        "tradeSponsor",
        "申购/赎回请求发起人",
        superAdmin
    )

    val tradeReview = PermDetail(
        "tradeReview",
        "申购/赎回审核",
        superAdmin
    )

    val portfolioProcessManager = PermDetail(
        "portfolioProcessManager",
        "产品流程管控",
        superAdmin
    )

    val portfolioEditingManager = PermDetail(
        "portfolioEditingManager",
        "产品编辑管理员",
        superAdmin
    )

    val portfolioOperation = PermDetail(
        "portfolioOperation",
        "产品运营",
        portfolioProcessManager
    )

    val portfolioMarketing = PermDetail(
        "portfolioMarketing",
        "产品市场",
        portfolioProcessManager
    )

    val portfolioSalesSupport = PermDetail(
        "portfolioSalesSupport",
        "产品销售支持",
        portfolioMarketing
    )

    val portfolioTrader = PermDetail(
        "portfolioTrader",
        "产品交易",
        portfolioProcessManager
    )

    val portfolioContractManager = PermDetail(
        "portfolioContractManager",
        "产品合同管理者",
        superAdmin
    )

    val portfolioContractSender = PermDetail(
        "portfolioContractSender",
        "产品销售支持，合同寄送",
        superAdmin
    )

    val portfolioContractInvestorRecord = PermDetail(
        "portfolioContractInvestorRecord",
        "合同回收后录入投资人信息",
        superAdmin
    )

    val portfolioContractArchive = PermDetail(
        "portfolioContractArchive",
        "产品合同归档，录入存放地",
        superAdmin
    )

    val portfolioContractCustodianSender = PermDetail(
        "portfolioContractCustodianSender",
        "产品合同寄送托管",
        superAdmin
    )

    val portfolioContractInfoManager = PermDetail(
        "portfolioContractInfoManager",
        "产品合同信息管理者，可以编辑产品合同信息，无需走流程",
        superAdmin
    )

    val portfolioBrokerManager = PermDetail(
        "portfolioBrokerManager",
        "产品经纪端管理者，可以编辑经纪端状态及信息，无需走流程",
        superAdmin
    )

    val visitReportApproval = PermDetail(
        "visitReportApproval",
        "访问通用审批页面",
        null,
    )

    val reportApproval1 = PermDetail(
        "reportApproval1",
        "投价报告第一步审批",
        visitReportApproval
    )

    val reportApproval2 = PermDetail(
        "reportApproval2",
        "投价报告第二步审批",
        visitReportApproval
    )

    val reportApprovalPayment = PermDetail(
        "reportApprovalPayment",
        "新股缴款审批",
        null,
    )

    val researchDepartment = PermDetail(
        "researchDepartment",
        "research department账号专属权限",
        null
    )

    val fileManager = PermDetail(
        "fileManager",
        "档案管理员",
        superAdmin
    )

    val contractReceiveRecord = PermDetail(
        "contractReceiveRecord",
        "合同回收，记录信息",
        superAdmin
    )

    val contractReceiveArchive = PermDetail(
        "contractReceiveArchive",
        "合同回收，归档",
        superAdmin
    )

    val digitalSealApproval = PermDetail(
        "digitalSealApproval",
        "电子用印审批",
        superAdmin
    )

    val contractModifyUpdate = PermDetail(
        "contractModifyUpdate",
        "合同修改更新电子平台合同及回收历史合同",
        superAdmin
    )

    val allowPasswordLogin = PermDetail(
        "allowPasswordLogin",
        "允许使用密码登录",
        superAdmin
    )

    val tradeTest = PermDetail(
        "tradeTest",
        "交易测试账户",
        superAdmin
    )

    val clientTest = PermDetail(
        "clientTest",
        "客户管理测试",
        superAdmin
    )

    val deletePortfolioBroker = PermDetail(
        "deletePortfolioBroker",
        "允许删除产品经纪端",
        superAdmin
    )

    val ipolotTest = PermDetail(
        "ipolotTest",
        "打新模块测试",
        superAdmin
    )

    val ipoCompanyData = PermDetail(
        "ipoCompanyData",
        "新股日历",
        ipolotTest
    )

    val portfolioCompliance = PermDetail(
        "portfolioCompliance",
        "合规",
        portfolioProcessManager,
    )

    val complianceTest = PermDetail(
        "complianceTest",
        "合规相关测试",
        superAdmin
    )

    val agencyTest = PermDetail(
        "agencyTest",
        "销售商测试",
        superAdmin
    )

    val tradeProcessManager = PermDetail(
        "tradeProcessManager",
        "交易流程管控",
        superAdmin
    )

    val portfolioSizeWarningEditor = PermDetail(
        "portfolioSizeWarningEditor",
        "编辑规模预警",
        superAdmin
    )
}
