package com.yanfuinvest.operation.controller

import com.yanfuinvest.common.domain.http.CustomResponse
import com.yanfuinvest.operation.domain.UnderwriterFileUpdate
import com.yanfuinvest.operation.service.TradeDateService
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.stereotype.Controller
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.sql.Date

@Validated
@RestController
@RequestMapping("/operation/portfolio/trade_date")
class TradeDateController (
    private val  service:TradeDateService
){
    @GetMapping("")
    fun getAllData(): CustomResponse<List<Date>> {
        return CustomResponse.Ok(service.getAllTradeDate())
    }
}