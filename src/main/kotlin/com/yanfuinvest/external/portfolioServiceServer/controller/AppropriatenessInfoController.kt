package com.yanfuinvest.external.portfolioServiceServer.controller

import com.yanfuinvest.common.domain.http.CustomResponse
import com.yanfuinvest.external.portfolioServiceServer.service.AppropriatenessBaseInfoService
import com.yanfuinvest.external.portfolioServiceServer.service.AppropriatenessTaxDeclarationService
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@Validated
@RestController
@RequestMapping("/external/portfolio_service_server/appropriateness")
class AppropriatenessInfoController(
    private val baseInfoService: AppropriatenessBaseInfoService,
    private val taxDeclarationService: AppropriatenessTaxDeclarationService
) {
    @GetMapping("base_info")
    fun getBaseInfoByUserId(@RequestParam userId: Int): CustomResponse<Any?> {
        return CustomResponse.Ok(baseInfoService.getByUserId(userId))
    }

    @GetMapping("tax_declaration")
    fun getTaxDeclarationByUserId(@RequestParam userId: Int): CustomResponse<Any?> {
        return CustomResponse.Ok(taxDeclarationService.getByUserId(userId))
    }
}