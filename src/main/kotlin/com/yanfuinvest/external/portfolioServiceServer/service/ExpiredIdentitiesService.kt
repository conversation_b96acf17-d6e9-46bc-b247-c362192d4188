package com.yanfuinvest.external.portfolioServiceServer.service

import com.yanfuinvest.common.domain.PaginationRequest
import com.yanfuinvest.external.portfolioServiceServer.domain.PortfolioServiceServerResponseCustom
import com.yanfuinvest.external.portfolioServiceServer.domain.dto.ExpiredIdentitiesReviewDTO
import com.yanfuinvest.external.portfolioServiceServer.utils.PortfolioServiceServerUtils
import com.yanfuinvest.operation.repository.NewInvestorBasicRepository
import com.yanfuinvest.operation.repository.PortfolioClientRepository
import org.springframework.core.ParameterizedTypeReference
import org.springframework.http.HttpMethod
import org.springframework.stereotype.Service

@Service
class ExpiredIdentitiesService(
    private val prefix: String = "user",
    private val portfolioClientRepository: PortfolioClientRepository,
    private val newInvestorBasicRepository: NewInvestorBasicRepository
): PortfolioServiceServerServiceBase<Any>() {

    override val getListPath = "$prefix.getExpiredIdentitiesList"
    private val responseType = object : ParameterizedTypeReference<PortfolioServiceServerResponseCustom<Any>>() {}

    fun getList(
        paginationRequest: PaginationRequest,
        filter: Map<String, Any?>?
    ): Pair<List<Any>, Long> {
        val paginationInput = PortfolioServiceServerUtils.parsePagination(paginationRequest)
        val input = paginationInput.plus(filter ?: emptyMap())
        return getList(input)
    }

    fun review(data: ExpiredIdentitiesReviewDTO){

        val input = mapOf("isApproved" to data.approved,"expiredIdentityId" to data.expiredIdentityId)

        request("$prefix.review", HttpMethod.POST, input)

        if (data.approved) {

            val client = portfolioClientRepository.findByIdentityNumber(data.identityNumber)

            val investorBasic = client.id?.let { newInvestorBasicRepository.findByClient_Id(it) }
                    ?: throw RuntimeException("Investor basic info not found")
//            val startDate = LocalDate.parse(dto.dateRange!!.first())
//            val endDate = LocalDate.parse(dto.dateRange.last())


            investorBasic.validityPeriod = data.dateRange
            newInvestorBasicRepository.save(investorBasic)
        }
    }
}