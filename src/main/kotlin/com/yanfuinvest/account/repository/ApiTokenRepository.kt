package com.yanfuinvest.account.repository

import com.yanfuinvest.account.domain.ApiToken
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface ApiTokenRepository: JpaRepository<ApiToken, Long> {
    fun getByTokenHash(hash: String): ApiToken?

    fun getAllByUserId(userId: Long): List<ApiToken>

    fun deleteByUserIdAndId(userId: Long, id: Long)
}