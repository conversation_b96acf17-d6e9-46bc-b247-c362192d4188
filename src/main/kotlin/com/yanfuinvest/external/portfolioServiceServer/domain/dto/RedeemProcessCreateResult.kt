package com.yanfuinvest.external.portfolioServiceServer.domain.dto

import com.yanfuinvest.common.annotation.NoArg
import java.sql.Date


@NoArg
class RedeemProcessCreateResult(
    val id: Long,
    val portfolioId: Long,
    val identityNumber: String,
    val identityType: String?,
    val classification: String?,
    val username: String,
    val userType: String?,
    val shareAmount: Double,
    val time: String,
)