package com.yanfuinvest.operation.controller
//import com.yanfui.sendReportService
//import com.yanfui.sendReportService
import com.yanfuinvest.common.domain.http.CustomResponse
import com.yanfuinvest.operation.domain.SendReport
import com.yanfuinvest.operation.domain.dto.SelectTimeDTO
import com.yanfuinvest.operation.service.SendReportService
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import java.sql.Date

@RestController
@Validated
@RequestMapping("/operation/portfolio/sendReport")
//@PreAuthorize("hasPermission('gte', @perms.complianceTest)")
@PreAuthorize("hasPermission('gte', @perms.portfolioCompliance)")
class sendReportController(
    private val service: SendReportService
) {
    @PostMapping("/uploadPDF")
    fun  uploadMany(@RequestPart("fileData")  files: Array<MultipartFile>, @RequestPart("reportType") reportType:String,  @RequestPart("reportingCycle") reportingCycle:String): CustomResponse<Unit> {
        return CustomResponse.Ok(service.importPDF(files,reportType,reportingCycle))
    }

    @PostMapping("/uploadTypeExcel")
    fun  parseTypeExcel( @RequestParam("files") files:MultipartFile): CustomResponse<Unit>{
        return CustomResponse.Ok(service.uploadExcel(files))
    }
    @GetMapping("")
    fun getList():CustomResponse<List<SendReport>>{
        return CustomResponse.Ok(service.getList())
    }

    @PreAuthorize("hasPermission('gte', @perms.complianceTest)")
    @PostMapping("/sendAll")
    fun  sendAll( @RequestBody  dto: Array<Long>,@RequestParam shareDate:Date): CustomResponse<MutableList<String>>{
        return CustomResponse.Ok(service.sendAll(dto,shareDate))
    }

    @PostMapping("/uploadDegeleteExcel")
    fun  parseDexcel(@RequestParam("files")files:MultipartFile):CustomResponse<Unit>{
        return CustomResponse.Ok(service.uploaddExcel(files))
    }

    @PostMapping("/selectSendTime/{id}")
    fun selectTime(@PathVariable id:Long,@RequestBody dto:SelectTimeDTO):CustomResponse<Unit>{
        return CustomResponse.Ok(service.selectTime(id,dto))
    }
}
