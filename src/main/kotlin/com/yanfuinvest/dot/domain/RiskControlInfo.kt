package com.yanfuinvest.dot.domain

import javax.persistence.*

@Entity
@Table(name = "dot_risk_control_info", uniqueConstraints = [UniqueConstraint(columnNames = ["pf_code"])])
class RiskControlInfo(
    @Column(name = "_id")
    val legacyId: String? = null,

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Int? = null,

    @Column(name = "pf_code")
    val pfCode: String,

    var shortExposure: Int,

    var longExposure: Int,

    var stockPercentage: Int,

    var maxStockPercentage: Int,

    var extra: String? = null,
)