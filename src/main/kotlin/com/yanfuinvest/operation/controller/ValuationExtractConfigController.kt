package com.yanfuinvest.operation.controller

import com.yanfuinvest.common.domain.http.CustomResponse
import com.yanfuinvest.operation.domain.dto.ValuationExtractConfigDTO
import com.yanfuinvest.operation.domain.enums.ValuationExtractConfigType
import com.yanfuinvest.operation.service.ValuationExtractConfigService
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*

@Validated
@RestController
@RequestMapping("/operation/valuation_extract_config/")
class ValuationExtractConfigController(
    private val valuationExtractConfigService: ValuationExtractConfigService
) {
    @GetMapping("broker")
    fun getBrokerVECList() = CustomResponse.Ok(
        valuationExtractConfigService.getList(ValuationExtractConfigType.BROKER).map { ValuationExtractConfigDTO(it) }
    )

    @GetMapping("portfolio")
    fun getPortfolioVECList() = CustomResponse.Ok(
        valuationExtractConfigService.getList(ValuationExtractConfigType.PORTFOLIO).map { ValuationExtractConfigDTO(it) }
    )

    @GetMapping("{id}")
    fun getValuationExtractConfig(
        @PathVariable id: Long
    ): CustomResponse<ValuationExtractConfigDTO> =
        CustomResponse(data = ValuationExtractConfigDTO(valuationExtractConfigService.get(id)), message = "")

    @GetMapping("{pid}/broker")
    fun getBrokerVECByPortfolioId(
        @PathVariable pid: Long
    ): CustomResponse<ValuationExtractConfigDTO> =
        CustomResponse.Ok(data = ValuationExtractConfigDTO(valuationExtractConfigService.getBrokerVECByPortfolioId(pid)))

    @PostMapping("broker")
    fun createBrokerVEC(
        @RequestBody valuationExtractConfigDTO: ValuationExtractConfigDTO
    ): CustomResponse<ValuationExtractConfigDTO> = CustomResponse(
        data = valuationExtractConfigService.createValuationExtractConfig(
            ValuationExtractConfigType.BROKER,
            valuationExtractConfigDTO
        ),
        message = "添加成功"
    )

    @PostMapping("portfolio")
    fun createPortfolioVEC(
        @RequestBody valuationExtractConfigDTO: ValuationExtractConfigDTO
    ): CustomResponse<ValuationExtractConfigDTO> = CustomResponse(
        data = valuationExtractConfigService.createValuationExtractConfig(
            ValuationExtractConfigType.PORTFOLIO,
            valuationExtractConfigDTO
        ),
        message = "添加成功"
    )

    @PutMapping("{id}")
    fun updateValuationExtractConfig(
        @PathVariable id: Long,
        @RequestBody valuationExtractConfigDTO: ValuationExtractConfigDTO
    ): CustomResponse<ValuationExtractConfigDTO> = CustomResponse(
        data = valuationExtractConfigService.updateValuationExtractConfig(id, valuationExtractConfigDTO),
        message = "更新成功"
    )

    @DeleteMapping("{id}")
    fun deleteValuationExtractConfig(
        @PathVariable id: Long
    ): CustomResponse<Unit> =
        CustomResponse(data = valuationExtractConfigService.deleteValuationExtractConfig(id), message = "删除成功")

}