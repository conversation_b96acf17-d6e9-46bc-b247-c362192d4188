package com.yanfuinvest.common.configuration

import org.bouncycastle.jce.provider.BouncyCastleProvider
import org.springframework.beans.factory.InitializingBean
import org.springframework.stereotype.Component
import java.security.Security
import java.util.*

@Component
class InitializingBeanImpl: InitializingBean {
    override fun afterPropertiesSet() {
        Locale.setDefault(Locale.ROOT)
        Security.addProvider(BouncyCastleProvider())
    }
}