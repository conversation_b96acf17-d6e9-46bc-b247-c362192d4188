package com.yanfuinvest.common.utils.excelConvertor

import com.alibaba.excel.converters.Converter
import com.alibaba.excel.converters.ReadConverterContext
import com.alibaba.excel.converters.WriteConverterContext
import com.alibaba.excel.enums.CellDataTypeEnum
import com.alibaba.excel.metadata.data.WriteCellData
import java.sql.Timestamp
import java.text.SimpleDateFormat
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

class TimestampConvertor: Converter<Timestamp> {
    private val pattern = "yyyy-MM-dd HH:mm:ss"
    private val formatter = SimpleDateFormat(pattern)
    private val parser = DateTimeFormatter.ofPattern(pattern)

    override fun supportJavaTypeKey(): Class<Timestamp> {
        return Timestamp::class.java
    }

    override fun supportExcelTypeKey(): CellDataTypeEnum {
        return CellDataTypeEnum.STRING
    }

    override fun convertToExcelData(context: WriteConverterContext<Timestamp>?): WriteCellData<String> {
        return WriteCellData(formatter.format(context!!.value))
    }

    override fun convertToJavaData(context: ReadConverterContext<*>?): Timestamp? {
        val value = context?.readCellData?.stringValue ?: return null
        try {
            return Timestamp.valueOf(LocalDateTime.parse(value, parser))
        } catch (_: Exception) {
            return null
        }
    }
}
