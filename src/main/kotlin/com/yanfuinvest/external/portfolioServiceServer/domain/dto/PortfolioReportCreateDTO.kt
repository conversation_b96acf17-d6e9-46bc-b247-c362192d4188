package com.yanfuinvest.external.portfolioServiceServer.domain.dto

import com.yanfuinvest.common.annotation.NoArg

@NoArg
class PortfolioReportCreateDTO(
    val portfolioId: Long?,
    val date: String?,
    val published: Boolean?,
    val fileUploadId: String?,
    var publishedBy: Long?,
    val includeClassAB: Boolean?,
) {
    fun withPortfolioId(portfolioId: Long?) =
        PortfolioReportCreateDTO(
            portfolioId = portfolioId,
            date = date,
            published = published,
            fileUploadId = fileUploadId,
            publishedBy = publishedBy,
            includeClassAB = includeClassAB,
        )
}