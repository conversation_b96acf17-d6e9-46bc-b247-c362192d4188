package com.yanfuinvest.external.portfolioServiceServer.service

import com.yanfuinvest.external.portfolioServiceServer.domain.PortfolioServiceServerResponseCustom
import org.springframework.core.ParameterizedTypeReference
import org.springframework.http.HttpMethod
import org.springframework.stereotype.Service

@Service
class WorkflowHistoryService(
    private val prefix: String = "workflowHistory"
) : PortfolioServiceServerServiceBase<Any>() {
    override val getListPath = "$prefix.list"

    private val listByIdResponseType =
        object : ParameterizedTypeReference<PortfolioServiceServerResponseCustom<Any>>() {}

    fun getListById(id: String): Any? {
        val input = mapOf("id" to id)
        val result = request("$prefix.listById", HttpMethod.GET, input, listByIdResponseType)
        return result?.result?.data
    }
}