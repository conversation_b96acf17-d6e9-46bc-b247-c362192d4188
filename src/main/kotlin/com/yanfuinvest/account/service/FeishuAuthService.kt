package com.yanfuinvest.account.service

import com.yanfuinvest.account.domain.User
import com.yanfuinvest.account.repository.UserRepository
import com.yanfuinvest.common.domain.exception.InvalidParamsException
import com.yanfuinvest.common.domain.exception.NotFoundException
import com.yanfuinvest.social.feishu.domain.FeishuUserByCodeReq
import com.yanfuinvest.social.feishu.service.FeishuService
import org.springframework.stereotype.Service

@Service
class FeishuAuthService(private val feishuService: FeishuService, private val userRepo: UserRepository) {
    fun feishuLogin(data: FeishuUserByCodeReq): User {
        val feishuUser = feishuService.getUserByCode(data) ?: throw InvalidParamsException("飞书认证失败")
        return userRepo.findByOpenId(feishuUser.openId) ?: throw NotFoundException("未找到绑定用户")
    }
}
