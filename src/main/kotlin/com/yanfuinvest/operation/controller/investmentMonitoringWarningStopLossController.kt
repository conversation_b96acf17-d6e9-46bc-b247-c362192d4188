package com.yanfuinvest.operation.controller

import com.yanfuinvest.common.domain.http.CustomResponse
import com.yanfuinvest.operation.domain.IPOCompanyData
import com.yanfuinvest.operation.domain.InvestmentMonitoringWarningStopLoss
import com.yanfuinvest.operation.domain.dto.WarningStopLossDTO
import com.yanfuinvest.operation.service.InvestmentMonitoringProductReviewService
import com.yanfuinvest.operation.service.InvestmentMonitoringWarningStopLossService
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@Controller
@RestController
@RequestMapping("/operation/monitorWarningStopLoss")
class investmentMonitoringWarningStopLossController (
    private val  service: InvestmentMonitoringWarningStopLossService
){
    @GetMapping("/getWarningData")
    fun getWarningData(): CustomResponse<List<WarningStopLossDTO>> {
        return CustomResponse.Ok(service.getWarningData())
    }

    @GetMapping("/getAllLatestNetValues")
    fun getAllLatestNetValues(): CustomResponse<List<WarningStopLossDTO>> {
        return CustomResponse.Ok(service.getAllLatestNetValues())
    }

    @GetMapping("/getMonitoringExecutionHistory")
    fun getMonitoringExecutionHistory():CustomResponse<List<InvestmentMonitoringWarningStopLoss>>{
        return CustomResponse.Ok(service.getMonitoringExecutionHistory())
    }
}