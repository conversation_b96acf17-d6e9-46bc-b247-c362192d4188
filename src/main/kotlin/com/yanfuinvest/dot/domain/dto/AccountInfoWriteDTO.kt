package com.yanfuinvest.dot.domain.dto

import com.yanfuinvest.common.annotation.NoArg
import com.yanfuinvest.dot.domain.enums.AccountInfoTypeEnum
import com.yanfuinvest.dot.domain.enums.PortfolioInfoStatusEnum

@NoArg
class AccountInfoWriteDTO(
    val pfCode: String,
    val accountId: String,
    val capitalPwd: String?,
    val commission: String?,
    val company: String?,
    val status: PortfolioInfoStatusEnum?,
    val tradingPwd: String?,
    val type: AccountInfoTypeEnum?,
    val valid: Boolean?,
    val client: String?,
    val extra: String?,
    val communicationPwd: String?,
    var monitorAccount: String?,
)