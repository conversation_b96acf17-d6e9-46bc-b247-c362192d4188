package com.yanfuinvest.common.configuration

import com.yanfuinvest.account.domain.User
import org.springframework.data.domain.AuditorAware
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Component
import java.util.*

@Component
class SpringSecurityAuditorAware: AuditorAware<User> {
    override fun getCurrentAuditor(): Optional<User> {
        val principal = SecurityContextHolder.getContext().authentication?.principal
        return if (principal is User) {
            Optional.ofNullable(principal)
        } else {
            Optional.empty()
        }
    }
}
