package com.yanfuinvest.external.portfolioServiceServer.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.yanfuinvest.common.extension.rest.execute
import com.yanfuinvest.external.portfolioServiceServer.domain.PortfolioServiceServerResponseList
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.core.ParameterizedTypeReference
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.web.client.RestTemplate

abstract class PortfolioServiceServerServiceBase<T> {
    protected open val getListPath: String? = null
    protected open val listResponseType =
        object : ParameterizedTypeReference<PortfolioServiceServerResponseList<T>>() {}

    @Value("\${yanfu.portfolioServiceServerUrl}")
    protected lateinit var serverUrl: String

    @Value("\${yanfu.portfolioServiceServer.secret}")
    protected lateinit var secret: String

    @Autowired
    protected lateinit var restTemplate: RestTemplate

    private val mapper = ObjectMapper()

    protected fun getList(input: Map<String, Any?>): Pair<List<T>, Long> {
        val uriVariables = mapOf("input" to mapper.writeValueAsString(input))
        val uri = "${serverUrl}/trpc/${getListPath}?input={input}"

        val response = restTemplate.execute {
            exchange(uri, HttpMethod.GET, getRequestEntity(), listResponseType, uriVariables)
        }
        val responseData = response.body?.result?.data ?: return Pair(listOf(), 0)

        return Pair(responseData.items, responseData.amount)
    }

    protected fun getRequestEntity(body: Any? = null, headers: HttpHeaders? = null): HttpEntity<Any> {
        return HttpEntity(body, HttpHeaders().also {
            it.add("secret", secret)
            if (headers != null) it.addAll(headers)
        })
    }

    protected fun request(path: String, method: HttpMethod, input: Any?): Any? {
        val uri = "${serverUrl}/trpc/${path}"
        val responseType = Any::class.java

        val response = if (method == HttpMethod.GET) {
            val uriVariables = mapOf("input" to mapper.writeValueAsString(input))
            val entity = getRequestEntity()
            restTemplate.execute { exchange("$uri?input={input}", method, entity, responseType, uriVariables) }
        } else {
            val entity = getRequestEntity(input)
            restTemplate.execute { exchange(uri, method, entity, responseType) }
        }

        return response.body
    }

    protected fun <T> request(
        path: String,
        method: HttpMethod,
        input: Any?,
        responseType: ParameterizedTypeReference<T>
    ): T? {
        val uri = "${serverUrl}/trpc/${path}"

        val response = if (method == HttpMethod.GET) {
            val uriVariables = mapOf("input" to mapper.writeValueAsString(input))
            val entity = getRequestEntity()
            restTemplate.execute { exchange("$uri?input={input}", method, entity, responseType, uriVariables) }
        } else {
            val entity = getRequestEntity(input)
            restTemplate.execute { exchange(uri, method, entity, responseType) }
        }

        return response.body
    }
}