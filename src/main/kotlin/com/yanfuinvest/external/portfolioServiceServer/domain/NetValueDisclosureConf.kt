package com.yanfuinvest.external.portfolioServiceServer.domain

import com.alibaba.excel.annotation.ExcelIgnore
import com.alibaba.excel.annotation.ExcelProperty

class NetValueDisclosureConf {
    @ExcelIgnore
    var portfolioId: Long? = null

    @ExcelProperty("设置情况")
    var status: String? = null

    @ExcelProperty("产品名称")
    var portfolioName: String? = null

    @ExcelProperty("产品编号")
    var portfolioNo: String? = null

    @ExcelProperty("成立日")
    var portfolioPublishDate: String? = null

    @ExcelProperty("净值披露周期")
    var disclosePeriod: String? = null

    @ExcelProperty("运行状态")
    var portfolioState: String? = null

    @ExcelProperty("净值披露起始日期")
    var startDate: String? = null
}