package com.yanfuinvest.external.portfolioServiceServer.controller

import com.yanfuinvest.account.domain.User
import com.yanfuinvest.common.domain.PaginationRequest
import com.yanfuinvest.common.domain.http.CustomResponse
import com.yanfuinvest.external.portfolioServiceServer.service.SupplementCheckLogService
import com.yanfuinvest.external.portfolioServiceServer.service.SupplementProcessService
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*

@Validated
@RestController
@RequestMapping("/external/portfolio_service_server/supplement_process")
class SupplementProcessController(
    private val service: SupplementProcessService,
    private val logService: SupplementCheckLogService,
) {
    @PostMapping("list")
    fun getList(
        @RequestParam page: Int,
        @RequestParam pageSize: Int,
        @RequestBody(required = false) filter: Map<String, Any?>?
    ): CustomResponse<List<Any>> {
        val (result, amount) = service.getList(PaginationRequest(page, pageSize), filter)
        return CustomResponse.Ok(data = result, amount = amount)
    }

    @GetMapping("supplement_links/{id}")
    fun getSupplementLinksById(@PathVariable id: Int) =
        CustomResponse.Ok(service.getSupplementLinksById(id))

    @GetMapping("documents/{id}")
    fun getDocumentsById(@PathVariable id: Int) = CustomResponse.Ok(service.getDocumentsById(id))

    @PostMapping("create")
    fun create(@AuthenticationPrincipal user: User, @RequestBody data: Map<String, Any?>) =
        CustomResponse.Ok(service.create(user, data))

    @PostMapping("review/{path}")
    fun review(
        @AuthenticationPrincipal user: User,
        @PathVariable("path") path: String,
        @RequestBody data: Map<String, Any?>,
    ) = CustomResponse.Ok(service.review(user, path, data))

    @PostMapping("log_list")
    fun getLogList(
        @RequestParam page: Int,
        @RequestParam pageSize: Int,
        @RequestBody(required = false) filter: Map<String, Any?>?
    ): CustomResponse<List<Any>> {
        val (result, amount) = logService.getList(PaginationRequest(page, pageSize), filter)
        return CustomResponse.Ok(data = result, amount = amount)
    }

    @PostMapping("terminate")
    fun terminate(
        @AuthenticationPrincipal user: User,
        @RequestBody data: Map<String, Any?>,
    ) = CustomResponse.Ok(service.terminate(user, data))
}