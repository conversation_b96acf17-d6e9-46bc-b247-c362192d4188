package com.yanfuinvest.common.configuration

import com.yanfuinvest.common.utils.CustomPropertyWriter
import org.springframework.beans.BeanUtils
import org.springframework.data.jpa.repository.support.JpaEntityInformation
import org.springframework.data.jpa.repository.support.SimpleJpaRepository
import org.springframework.data.repository.findByIdOrNull
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.context.request.RequestContextHolder
import org.springframework.web.context.request.ServletRequestAttributes
import javax.persistence.EntityManager

class JpaRepositoryImpl<T : Any, ID>(
    private val entityInformation: JpaEntityInformation<T, ID>,
    private val entityManager: EntityManager,
) : SimpleJpaRepository<T, ID>(entityInformation, entityManager) {
    @Transactional
    override fun <S : T> save(entity: S): S {
        if (entityInformation.isNew(entity)) {
            entityManager.persist(entity)
            return entity
        }

        val filterPropertyNames = getFilterPropertyNames() ?: return entityManager.merge(entity)
        val existingEntity = getExistingEntity(entity) ?: return entityManager.merge(entity)

        BeanUtils.copyProperties(entity, existingEntity, *filterPropertyNames.toTypedArray())

        entityManager.merge(existingEntity)
        return entity
    }

    private fun getExistingEntity(entity: T): T? {
        val id = entityInformation.getId(entity) ?: return null
        return findByIdOrNull(id)
    }

    private fun getFilterPropertyNames(): List<String>? {
        val requestAttribute = RequestContextHolder.currentRequestAttributes() as ServletRequestAttributes
        val request = requestAttribute.request

        val filterMap = request.getAttribute(CustomPropertyWriter.FILTER_NAME)
        if (filterMap !is Map<*, *>) return null

        val propertyNames = filterMap.keys.mapNotNull { if (it is String) it else null }
        if (propertyNames.isEmpty()) return null

        return propertyNames
    }
}