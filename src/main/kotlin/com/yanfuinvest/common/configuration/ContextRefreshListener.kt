package com.yanfuinvest.common.configuration

import com.yanfuinvest.meta.service.EndPointService
import org.springframework.context.ApplicationListener
import org.springframework.context.event.ContextRefreshedEvent
import org.springframework.stereotype.Component

@Component
class ContextRefreshListener(private val endPointService: EndPointService): ApplicationListener<ContextRefreshedEvent> {
    override fun onApplicationEvent(event: ContextRefreshedEvent) {
        endPointService.refreshEndPoints()
    }
}