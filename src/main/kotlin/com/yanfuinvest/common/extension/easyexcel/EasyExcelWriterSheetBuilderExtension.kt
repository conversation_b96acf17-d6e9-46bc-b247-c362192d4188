package com.yanfuinvest.common.extension.easyexcel

import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder
import com.yanfuinvest.common.controller.RestControllerResponseAdvice
import com.yanfuinvest.common.domain.exception.InvalidParamsException
import com.yanfuinvest.common.domain.http.CustomResponse
import com.yanfuinvest.common.utils.CustomPropertyWriter
import org.springframework.expression.spel.standard.SpelExpressionParser
import org.springframework.expression.spel.support.StandardEvaluationContext
import org.springframework.web.context.request.RequestContextHolder
import org.springframework.web.context.request.ServletRequestAttributes
import kotlin.reflect.full.memberProperties

fun ExcelWriterSheetBuilder.filterRecord(data: Collection<*>): Collection<*> {
    val requestAttribute = RequestContextHolder.currentRequestAttributes() as ServletRequestAttributes
    val request = requestAttribute.request

    val result = RestControllerResponseAdvice.filter(CustomResponse(data = data), request).data
    if (result !is Collection<*>) throw InvalidParamsException("filtered data is not a collection")

    return result
}

fun ExcelWriterSheetBuilder.filterProperty(data: Collection<*>): Collection<*> {
    val expression = CustomPropertyWriter.getExpression() ?: return data

    val parser = SpelExpressionParser()

    for (item in data) {
        if (item == null) continue

        for (property in item::class.memberProperties) {
            val name = property.name
            if (!expression.contains(name)) continue

            val context = StandardEvaluationContext()
            context.setVariables(mapOf("bean" to item, "name" to name))
            if (parser.parseExpression(expression).getValue(context) != true) continue

            parser.parseExpression("$name = null").getValue(StandardEvaluationContext(item))
        }
    }

    return data
}

fun ExcelWriterSheetBuilder.filterAndDoWrite(data: Collection<*>) {
    doWrite(filterProperty(filterRecord(data)))
}

fun ExcelWriterSheetBuilder.filterAndDoFill(data: Collection<*>) {
    doFill(filterProperty(filterRecord(data)))
}