package com.yanfuinvest.external.portfolioServiceServer.service

import com.yanfuinvest.external.portfolioServiceServer.domain.PortfolioServiceServerResponseCustom
import org.springframework.core.ParameterizedTypeReference
import org.springframework.http.HttpMethod
import org.springframework.stereotype.Service

@Service
class VideoRecordLegacyService(
    private val prefix: String = "videoRecordLegacy"
) : PortfolioServiceServerServiceBase<Any>() {
    private val responseType = object : ParameterizedTypeReference<PortfolioServiceServerResponseCustom<Any>>() {}

    fun getList(userId: Int?, identityNumber: String?, portfolioId: Long?): Any? {
        val input = mapOf(
            "userId" to userId,
            "identityNumber" to identityNumber,
            "portfolioId" to portfolioId
        )
        val result = request("$prefix.list", HttpMethod.GET, input, responseType)
        return result?.result?.data
    }
}