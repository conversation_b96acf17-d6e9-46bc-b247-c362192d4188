package com.yanfuinvest.external.portfolioServiceServer.controller

import com.yanfuinvest.account.domain.User
import com.yanfuinvest.common.domain.PaginationRequest
import com.yanfuinvest.common.domain.http.CustomResponse
import com.yanfuinvest.external.portfolioServiceServer.service.PortfolioServiceRedeemProcessService
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*

@Validated
@RestController
@RequestMapping("/external/portfolio_service_server/redeem_process")
class RedeemProcessController(
    private val service: PortfolioServiceRedeemProcessService
) {
    @PostMapping("list")
    fun getList(
        @RequestParam page: Int,
        @RequestParam pageSize: Int,
        @RequestBody(required = false) filter: Map<String, Any?>?
    ): CustomResponse<List<Any>> {
        val (result, amount) = service.getList(PaginationRequest(page, pageSize), filter)
        return CustomResponse.Ok(data = result, amount = amount)
    }

    @GetMapping("{id}")
    fun getById(@PathVariable id: Long): CustomResponse<Any?> =
        CustomResponse.Ok(service.getById(id))

    @GetMapping("list/{identityNumber}")
    fun getListByIdentityNumber(@PathVariable identityNumber: String) =
        CustomResponse.Ok(service.getListByIdentityNumber(identityNumber))

    @GetMapping("documents/{id}")
    fun getDocumentsById(@PathVariable id: Int) = CustomResponse.Ok(service.getDocumentsById(id))

    @PostMapping("create")
    fun create(@AuthenticationPrincipal user: User, @RequestBody data: Map<String, Any?>) =
        CustomResponse.Ok(service.create(user, data))

    @PostMapping("review/{path}")
    fun review(
        @AuthenticationPrincipal user: User,
        @PathVariable("path") path: String,
        @RequestBody data: Map<String, Any?>,
    ) = CustomResponse.Ok(service.review(user, path, data))

    @PostMapping("terminate")
    fun terminate(
        @AuthenticationPrincipal user: User,
        @RequestBody data: Map<String, Any?>,
    ) = CustomResponse.Ok(service.terminate(user, data))
}