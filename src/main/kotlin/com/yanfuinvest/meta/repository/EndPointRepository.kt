package com.yanfuinvest.meta.repository

import com.yanfuinvest.meta.domain.EndPoint
import com.yanfuinvest.meta.domain.EndPointId
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface EndPointRepository: JpaRepository<EndPoint, EndPointId> {
    @Query("select e.publicToAll from EndPoint e where e.method = :method and e.pattern = :pattern")
    fun isPublic(method: String, pattern: String): Boolean?
}
