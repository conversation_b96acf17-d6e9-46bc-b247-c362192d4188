package com.yanfuinvest.operation.controller

import com.yanfuinvest.account.domain.User
import com.yanfuinvest.common.domain.http.CustomResponse
import com.yanfuinvest.operation.domain.StonecloudFileDelete
import com.yanfuinvest.operation.domain.dto.StonecloudFileDeleteStartDTO
import com.yanfuinvest.operation.service.StonecloueFileDeleteService
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*

@Validated
@RestController
@RequestMapping("/operation/portfolio/stonecloud_file_delete")
class StonecloudFileDeleteController(
    private val service: StonecloueFileDeleteService,
) {
    @GetMapping("")
    fun getListByIdentityNumber(@RequestParam identityNumber: String) =
        CustomResponse.Ok(service.getListByIdentityNumber(identityNumber))

    @GetMapping("/{taskId}")
    fun findNewestByTaskId(@PathVariable taskId: String) = CustomResponse.Ok(service.findNewestByTaskId(taskId))

    @PostMapping("")
    fun startProcess(
        @AuthenticationPrincipal user: User,
        @RequestBody data: StonecloudFileDeleteStartDTO,
    ): CustomResponse<StonecloudFileDelete> {
        val result = service.startProcess(user, data)
        return CustomResponse(data = result, message = "发起申请成功")
    }
}