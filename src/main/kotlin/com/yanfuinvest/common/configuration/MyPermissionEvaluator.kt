package com.yanfuinvest.common.configuration

import com.yanfuinvest.account.domain.User
import com.yanfuinvest.common.domain.constant.Perms
import org.springframework.beans.factory.annotation.Value
import org.springframework.security.access.PermissionEvaluator
import org.springframework.security.core.Authentication
import org.springframework.stereotype.Component
import java.io.Serializable
import javax.servlet.http.HttpServletRequest

@Component
class MyPermissionEvaluator (
    private val request: HttpServletRequest,
    @Value("\${yanfu.serviceIp}")
    private val serviceIp: String
): PermissionEvaluator {
    override fun hasPermission(authentication: Authentication?, targetDomainObject: Any?, permission: Any?): Bo<PERSON>an {
        return if (targetDomainObject != null && targetDomainObject == "fromLocal") {
            // 从服务器本机发出的请求
            val ip: String? = request.getHeader("X-real-ip")
            println("ip address for validation: $ip")
            ip != null && ip == serviceIp
        } else if (authentication == null || targetDomainObject == null) {
            false
        } else {
            if (authentication.principal is User) {
                val user = authentication.principal as User

                if (targetDomainObject == "pwdAvailable") {
                    // 无需重置密码则获得该权限
                    !user.pwdResetRequired
                } else if (targetDomainObject == "gte") {
                    // 用户所有权限为所给权限的祖先
                    if (permission is Perms.PermDetail) {
                        var curPerm: Perms.PermDetail? = permission
                        var result = false
                        while (curPerm != null) {
                            if (user.authorities.any { it.authority == curPerm!!.name }) {
                                result = true
                                break
                            }
                            curPerm = curPerm.parent
                        }
                        result
                    } else {
                        false
                    }
                } else {
                    false
                }
            } else {
                false
            }
        }
    }

    override fun hasPermission(authentication: Authentication?, targetId: Serializable?, targetType: String?, permission: Any?): Boolean {
        TODO("Not yet implemented")
    }
}
