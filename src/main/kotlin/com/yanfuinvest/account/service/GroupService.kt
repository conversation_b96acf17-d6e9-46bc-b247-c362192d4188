package com.yanfuinvest.account.service

import com.querydsl.jpa.impl.JPAQueryFactory
import com.yanfuinvest.account.domain.Group
import com.yanfuinvest.account.domain.QGroup
import com.yanfuinvest.account.domain.QUser
import com.yanfuinvest.account.domain.User
import com.yanfuinvest.account.domain.constant.GroupTags
import com.yanfuinvest.account.domain.dto.*
import com.yanfuinvest.account.repository.GroupRepository
import com.yanfuinvest.account.repository.PermissionRepository
import com.yanfuinvest.account.repository.UserRepository
import com.yanfuinvest.common.domain.exception.NotFoundException
import com.yanfuinvest.operation.domain.dto.UserDTOMin
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import javax.persistence.EntityManager
import kotlin.reflect.full.memberProperties

@Service
class GroupService(
    private val repo: GroupRepository,
    private val userRepo: UserRepository,
    private val queryFactory: JPAQueryFactory,
    private val permissionRepo: PermissionRepository,
    private val entityManager: EntityManager,
    private val groupTags: GroupTags,
) {
    fun getList(userId: Long?): List<GroupReadDTO> {
        if (userId == null) return listOf()

        val qGroup = QGroup.group
        val groups = if (userRepo.superAdmin(userId) == true) {
            queryFactory
                .selectFrom(qGroup)
                .leftJoin(qGroup.owner).fetchJoin()
                .leftJoin(qGroup.permissions).fetchJoin()
                .leftJoin(qGroup.users).fetchJoin()
                .distinct()
                .fetch()
        } else {
            val qUser = QUser.user

            queryFactory
                .select(qGroup)
                .from(qUser)
                .leftJoin(qGroup).on(qUser.groups.contains(qGroup))
                .leftJoin(qGroup.owner).fetchJoin()
                .leftJoin(qGroup.permissions).fetchJoin()
                .leftJoin(qGroup.users).fetchJoin()
                .where(qUser.id.eq(userId).and(qGroup.owner.id.eq(userId)))
                .distinct()
                .fetch()
        }

        return groups.map {
            GroupReadDTO(
                id = it.id,
                name = it.name,
                description = it.description,
                tags = it.tags,
                ownerId = it.owner?.id,
                ownerName = it.owner.realName ?: it.owner.username,
                permissions = it.permissions,
                users = it.users.map { user ->
                    UserDTOMin(id = user.id, realName = user.realName, username = user.username)
                }.toSet()
            )
        }
    }

    fun get(user: User, id: Long): Group {
        return user.id?.let {
            if (user.superAdmin) repo.findByIdOrNull(id)
            else repo.findByIdAndOwnerId(id, it)
        } ?: throw NotFoundException("未找到记录")
    }

    @Transactional
    fun create(user: User, data: GroupWriteDTO): Group {
        val group = Group(name = data.name, description = data.description, tags = data.tags, owner = user)
        return repo.save(group)
    }

    @Transactional
    fun update(user: User, id: Long, data: GroupWriteDTO): Group {
        val group = get(user, id)

        group.name = data.name
        group.description = data.description
        group.tags = data.tags

        return repo.save(group)
    }

    @Transactional
    fun delete(user: User, id: Long) {
        entityManager
            .createNativeQuery("delete from user_groups where groups_id = $id")
            .executeUpdate()

        user.id?.let { repo.deleteByIdAndOwnerId(id, it) }
    }

    @Transactional
    fun setGroupPermissions(user: User, id: Long, data: GroupPermissionWriteDTO): Group {
        val group = get(user, id)
        val permissions = data.permissionIds?.let { permissionRepo.findAllById(it) } ?: listOf()

        group.permissions.clear()
        group.permissions.addAll(permissions)

        return repo.save(group)
    }

    @Transactional
    fun setGroupUsers(user: User, id: Long, data: GroupUserWriteDTO): Group {
        val group = get(user, id)

        val users = data.userIds?.let { userRepo.findAllById(it) } ?: listOf()
        val userIds = users.mapNotNull { it.id }

        entityManager.createNativeQuery("""
            DELETE FROM user_groups WHERE groups_id = ${group.id}
        """.trimIndent()).executeUpdate()

        if (userIds.isNotEmpty()) {
            entityManager.createNativeQuery("""
                INSERT INTO user_groups (users_id, groups_id) VALUES
                ${userIds.joinToString(",") { userId -> "($userId, ${group.id})" }} 
            """.trimIndent()).executeUpdate()
        }

        entityManager.flush()
        entityManager.refresh(group)

        return group
    }

    @Transactional
    fun setUserGroups(owner: User, data: UserGroupWriteDTO): User {
        val user = userRepo.findByIdOrNull(data.userId) ?: throw NotFoundException("用户不存在")

        val groups = user.id?.let { repo.findAllByIdInAndOwnerId(data.groupIds, it) } ?: listOf()

        user.groups.clear()
        user.groups.addAll(groups)

        return userRepo.save(user)
    }

    fun getTagList(): List<String> {
        return GroupTags::class.memberProperties.mapNotNull {
            val property = it.call(groupTags)
            return@mapNotNull if (property is GroupTags.GroupTag) property.name else null
        }
    }

    fun getUsersByTags(tags: List<GroupTags.GroupTag>): List<User> {
        return getUsersByTagNames(tags.map { it.name })
    }

    fun getUsersByTagNames(tags: List<String>): List<User> {
        val qUser = QUser.user
        val qGroup = QGroup.group
        val tagSet = tags.toSet()

        val groups = queryFactory.selectFrom(qGroup).fetch().filter { it.tags?.intersect(tagSet)?.isNotEmpty() == true }
        if (groups.isEmpty()) return listOf()

        val groupIds = groups.mapNotNull { it.id }

        return queryFactory
            .selectDistinct(qUser).from(qUser)
            .leftJoin(qGroup).on(qUser.`in`(qGroup.users))
            .where(qGroup.id.`in`(groupIds))
            .fetch()
    }
}
