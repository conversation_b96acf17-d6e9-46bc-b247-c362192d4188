package com.yanfuinvest.common.utils

import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.databind.SerializerProvider
import com.fasterxml.jackson.databind.ser.BeanPropertyWriter
import org.springframework.expression.spel.standard.SpelExpressionParser
import org.springframework.expression.spel.support.StandardEvaluationContext
import org.springframework.web.context.request.RequestContextHolder
import org.springframework.web.context.request.ServletRequestAttributes
import java.math.BigDecimal
import java.math.BigInteger

class CustomPropertyWriter(private val writer: BeanPropertyWriter): BeanPropertyWriter(writer) {
    private val parser = SpelExpressionParser()

    override fun serializeAsField(bean: Any, gen: JsonGenerator, prov: SerializerProvider) {
        val expression = getExpression()
        if (expression == null || !expression.contains(writer.name)) {
            writer.serializeAsField(bean, gen, prov)
            return
        }

        val context = StandardEvaluationContext()
        context.setVariables(mapOf("bean" to bean, "name" to writer.name))

        when (val result = parser.parseExpression(expression).getValue(context)) {
            is Boolean -> when (result) {
                true -> if (writer.type.rawClass.isAssignableFrom(String::class.java)) gen.writeStringField(writer.name, "")
                false -> writer.serializeAsField(bean, gen, prov)
            }

            is String -> gen.writeStringField(writer.name, result)

            is Short -> gen.writeNumberField(writer.name, result)
            is Int -> gen.writeNumberField(writer.name, result)
            is Long -> gen.writeNumberField(writer.name, result)
            is BigInteger -> gen.writeNumberField(writer.name, result)
            is Float -> gen.writeNumberField(writer.name, result)
            is Double -> gen.writeNumberField(writer.name, result)
            is BigDecimal -> gen.writeNumberField(writer.name, result)

            else -> writer.serializeAsField(bean, gen, prov)
        }
    }

    companion object {
        private const val PACKAGE_NAME = "com.yanfuinvest.common.utils.CustomPopertyWriter"
        const val FILTER_NAME = "$PACKAGE_NAME.filter"

        fun getExpression(): String? {
            val requestAttribute = RequestContextHolder.currentRequestAttributes() as ServletRequestAttributes
            val request = requestAttribute.request

            val expression = request.getAttribute(FILTER_NAME)
            if (expression !is String || expression.isEmpty()) return null

            return expression
        }
    }
}