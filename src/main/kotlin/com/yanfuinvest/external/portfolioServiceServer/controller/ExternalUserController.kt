package com.yanfuinvest.external.portfolioServiceServer.controller

import com.yanfuinvest.common.domain.PaginationRequest
import com.yanfuinvest.common.domain.http.CustomResponse
import com.yanfuinvest.external.portfolioServiceServer.service.ExternalUserConfigService
import com.yanfuinvest.external.portfolioServiceServer.service.ExternalUserService
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*

@Validated
@RestController
@RequestMapping("/external/portfolio_service_server/user")
class ExternalUserController(
    private val service: ExternalUserService,
    private val configService: ExternalUserConfigService,
) {
    @GetMapping("list")
    fun getList(
        @RequestParam page: Int,
        @RequestParam pageSize: Int,
        @RequestParam(required = false) name: String?,
        @RequestParam(required = false) identityNumber: String?,
    ): CustomResponse<List<Any>> {
        val filter = mapOf("name" to name, "identityNumber" to identityNumber)
        val (result, amount) = service.getList(PaginationRequest(page, pageSize), filter)
        return CustomResponse.Ok(data = result, amount = amount)
    }

    @GetMapping("list_by_name_fuzzy")
    fun getListByNameFuzzy(@RequestParam name: String) = CustomResponse.Ok(service.getListByNameFuzzy(name))

    @GetMapping("{id}/config")
    fun getConfigById(@PathVariable id: Long) = CustomResponse.Ok(configService.getByUserId(id))

    @PostMapping("{id}/config")
    fun updateConfig(@PathVariable id: Long, @RequestBody data: Map<String, Any?>) =
        CustomResponse.Ok(configService.update(mapOf("id" to id).plus(data)))
}