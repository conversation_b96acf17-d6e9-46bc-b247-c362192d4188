package com.yanfuinvest.external.portfolioServiceServer.service

import org.springframework.http.HttpMethod
import org.springframework.stereotype.Service

@Service
class VideoConfigWordsService(
    private val prefix: String = "videoConfig"
): PortfolioServiceServerServiceBase<Any>() {
    override val getListPath = "$prefix.list"

    fun getList(): Any? {
        return request("$prefix.wordsList", HttpMethod.GET, null)
    }

    fun create(data: Map<String, Any?>) {
        request("$prefix.createWords", HttpMethod.POST, data)
    }

    fun update(data: Map<String, Any?>) {
        request("$prefix.updateWords", HttpMethod.POST, data)
    }
}