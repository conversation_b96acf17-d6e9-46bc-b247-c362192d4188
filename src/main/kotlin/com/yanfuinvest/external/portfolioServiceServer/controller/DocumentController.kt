package com.yanfuinvest.external.portfolioServiceServer.controller

import com.yanfuinvest.common.domain.http.CustomResponse
import com.yanfuinvest.external.portfolioServiceServer.service.DocumentService
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@Validated
@RestController
@RequestMapping("/external/portfolio_service_server/document")
class DocumentController(
    private val service: DocumentService
) {
    @GetMapping("list")
    fun getList(@RequestParam userId: Int, @RequestParam type: String) =
        CustomResponse.Ok(service.getList(userId, type))

    @GetMapping("{id}")
    fun getById(@PathVariable id: Int) = CustomResponse.Ok(service.getById(id))

    @GetMapping("ttd_link/{id}")
    fun getTtdLinkById(@PathVariable("id") id: Int) =
        CustomResponse.Ok(service.getTtdLinkById(id))
}