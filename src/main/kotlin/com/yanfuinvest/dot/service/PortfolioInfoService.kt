package com.yanfuinvest.dot.service

import com.querydsl.core.BooleanBuilder
import com.querydsl.jpa.impl.JPAQueryFactory
import com.yanfuinvest.common.domain.PaginationRequest
import com.yanfuinvest.common.domain.exception.InvalidParamsException
import com.yanfuinvest.common.domain.exception.NotFoundException
import com.yanfuinvest.common.extension.repository.findByIdOrThrow
import com.yanfuinvest.dot.domain.PortfolioInfo
import com.yanfuinvest.dot.domain.QPortfolioInfo
import com.yanfuinvest.dot.domain.dto.PortfolioInfoFilter
import com.yanfuinvest.dot.domain.dto.PortfolioInfoWriteDTO
import com.yanfuinvest.dot.repository.AccountInfoRepository
import com.yanfuinvest.dot.repository.PortfolioInfoRepository
import com.yanfuinvest.dot.repository.RiskControlInfoRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import javax.persistence.LockModeType

@Service
class PortfolioInfoService(
    private val repo: PortfolioInfoRepository,
    private val queryFactory: JPAQueryFactory,
    private val riskControlInfoRepo: RiskControlInfoRepository,
    private val accountInfoRepo: AccountInfoRepository,
) {
    private val q = QPortfolioInfo.portfolioInfo

    fun getList(paginationRequest: PaginationRequest, filter: PortfolioInfoFilter?): Pair<List<PortfolioInfo>, Long> {
        val predicate = BooleanBuilder()

        predicate.and(q.valid.isNull)
        filter?.pfCode?.let { predicate.and(q.pfCode.contains(it)) }
        filter?.pfName?.let { predicate.and(q.pfName.contains(it)) }
        filter?.status?.let { predicate.and(q.status.eq(it)) }
        filter?.type?.let { predicate.and(q.type.eq(it)) }
        filter?.extra?.let { predicate.and(q.extra.contains(it)) }

        val queryBase = queryFactory.selectFrom(q).where(predicate)

        val queryResult = queryBase.clone().limit(paginationRequest.limit).offset(paginationRequest.offset).fetch()
        val amount = queryBase.clone().fetchCount()

        return Pair(queryResult, amount)
    }

    fun getById(id: Int) = repo.findByIdOrThrow(id)

    fun getForUpdate(id: Int): PortfolioInfo {
        val query = queryFactory.selectFrom(q).where(q.id.eq(id)).setLockMode(LockModeType.PESSIMISTIC_WRITE)
        return query.fetchOne() ?: throw NotFoundException("未找到记录")
    }

    fun getByPfCodeForUpdate(pfCode: String): PortfolioInfo? {
        val query = queryFactory.selectFrom(q).where(q.pfCode.eq(pfCode)).setLockMode(LockModeType.PESSIMISTIC_WRITE)
        return query.fetchOne()
    }

    @Transactional
    fun create(data: PortfolioInfoWriteDTO): PortfolioInfo {
        val pfCode = data.pfCode ?: throw InvalidParamsException("产品代码为空")
        getByPfCodeForUpdate(pfCode)?.let { throw InvalidParamsException("产品代码已存在") }

        val record = PortfolioInfo(
            pfCode = pfCode,
            pfName = data.pfName,
            status = data.status,
            type = data.type,
            valid = data.valid,
            extra = data.extra,
        )

        return repo.save(record)
    }

    @Transactional
    fun update(id: Int, data: PortfolioInfoWriteDTO) {
        val record = getForUpdate(id)

        record.pfCode = data.pfCode
        record.pfName = data.pfName
        record.status = data.status
        record.type = data.type
        record.valid = data.valid
        record.extra = data.extra

        repo.save(record)
    }

    @Transactional
    fun delete(id: Int) {
        val record = getForUpdate(id)
        record.pfCode?.let {
            riskControlInfoRepo.deleteAllByPfCode(it)
            accountInfoRepo.deleteAllByPfCode(it)
        }

        repo.deleteById(id)
    }
}