package com.yanfuinvest.external.portfolioServiceServer.domain.dto

import com.yanfuinvest.common.annotation.NoArg

@NoArg
class PeriodicReportCreateDTO(
    val portfolioId: Long?,
    val type: String?,
    val period: String?,
    val date: String?,
    val published: Boolean?,
    val fileUploadId: String?,
    var publishedBy: Long?,
    val includeClassAB: Boolean?,
) {
    fun withPortfolioId(portfolioId: Long?) =
        PeriodicReportCreateDTO(
            portfolioId = portfolioId,
            type = type,
            period = period,
            date = date,
            published = published,
            fileUploadId = fileUploadId,
            publishedBy = publishedBy,
            includeClassAB = includeClassAB,
        )
}