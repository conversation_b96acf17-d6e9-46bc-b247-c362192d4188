package com.yanfuinvest.external.portfolioServiceServer.controller

import com.yanfuinvest.common.domain.http.CustomResponse
import com.yanfuinvest.external.portfolioServiceServer.service.WorkflowHistoryService
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@Validated
@RestController
@RequestMapping("/external/portfolio_service_server/workflow_history")
class WorkflowHistoryController(
    private val service: WorkflowHistoryService
) {
    @GetMapping("{workflowId}")
    fun getHistoryListById(@PathVariable workflowId: String) =
        CustomResponse.Ok(service.getListById(workflowId))
}