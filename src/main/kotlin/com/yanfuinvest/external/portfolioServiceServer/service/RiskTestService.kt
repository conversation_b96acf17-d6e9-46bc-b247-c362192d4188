package com.yanfuinvest.external.portfolioServiceServer.service

import com.yanfuinvest.common.domain.PaginationRequest
import com.yanfuinvest.external.portfolioServiceServer.domain.PortfolioServiceServerResponseCustom
import com.yanfuinvest.external.portfolioServiceServer.utils.PortfolioServiceServerUtils
import org.springframework.core.ParameterizedTypeReference
import org.springframework.http.HttpMethod
import org.springframework.stereotype.Service

@Service
class RiskTestService(
    private val prefix: String = "riskTest"
): PortfolioServiceServerServiceBase<Any>() {
    override val getListPath = "$prefix.list"
    private val responseType = object : ParameterizedTypeReference<PortfolioServiceServerResponseCustom<Any>>() {}

    fun getList(
        paginationRequest: PaginationRequest,
        userId: Int?,
    ): Pair<List<Any>, Long> {
        val paginationInput = PortfolioServiceServerUtils.parsePagination(paginationRequest)
        val input = paginationInput.plus(userId?.let { mapOf("userId" to userId) } ?: emptyMap())

        return getList(input)
    }

    fun getListByIdentityNumber(identityNumber: String): Any? {
        val input = mapOf("identityNumber" to identityNumber)
        val result = request("$prefix.listByIdentityNumber", HttpMethod.GET, input, responseType)

        return result?.result?.data
    }

    fun getById(id: Int): Any? {
        val input = mapOf("id" to id)
        val result = request("$prefix.byId", HttpMethod.GET, input, responseType)
        return result?.result?.data
    }

    fun getQuestionnaireById(id: Int): Any? {
        val input = mapOf("id" to id)
        val result = request("$prefix.questionnaireLinkById", HttpMethod.GET, input, responseType)
        return result?.result?.data
    }
}