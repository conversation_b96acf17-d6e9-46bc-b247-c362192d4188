package com.yanfuinvest.common.utils.excelConvertor

import com.alibaba.excel.converters.Converter
import com.alibaba.excel.converters.ReadConverterContext
import com.alibaba.excel.converters.WriteConverterContext
import com.alibaba.excel.enums.CellDataTypeEnum
import com.alibaba.excel.metadata.data.WriteCellData
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.sql.Date

class DateConvertor: Converter<Date> {
    private val pattern = "yyyy-MM-dd"
    private val formatter = SimpleDateFormat(pattern)
    private val parser = DateTimeFormatter.ofPattern(pattern)

    override fun supportJavaTypeKey(): Class<Date> {
        return Date::class.java
    }

    override fun supportExcelTypeKey(): CellDataTypeEnum {
        return CellDataTypeEnum.STRING
    }

    override fun convertToExcelData(context: WriteConverterContext<Date>?): WriteCellData<String> {
        return WriteCellData(formatter.format(context!!.value))
    }

    override fun convertToJavaData(context: ReadConverterContext<*>?): Date? {
        val value = context?.readCellData?.stringValue ?: return null
        try {
            return Date.valueOf(LocalDate.parse(value, parser))
        } catch (_: Exception) {
            return null
        }
    }
}