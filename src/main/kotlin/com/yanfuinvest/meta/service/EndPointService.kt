package com.yanfuinvest.meta.service

import com.querydsl.jpa.impl.JPAQuery
import com.querydsl.jpa.impl.JPAQueryFactory
import com.yanfuinvest.account.domain.QGroup
import com.yanfuinvest.account.domain.QPermission
import com.yanfuinvest.account.domain.QUser
import com.yanfuinvest.account.domain.User
import com.yanfuinvest.account.repository.UserRepository
import com.yanfuinvest.meta.domain.EndPoint
import com.yanfuinvest.meta.domain.EndPointId
import com.yanfuinvest.meta.domain.QEndPoint
import com.yanfuinvest.meta.repository.EndPointRepository
import io.swagger.annotations.ApiOperation
import javassist.NotFoundException
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping

@Service
class EndPointService(
    private val requestMappingHandlerMapping: RequestMappingHandlerMapping,
    private val repo: EndPointRepository,
    private val queryFactory: JPAQueryFactory,
    private val userRepo: UserRepository,
) {
    fun getActiveList(): List<EndPointId> {
        val handlerMethods = requestMappingHandlerMapping.handlerMethods
        return handlerMethods.mapNotNull { (info) ->
            val method = info.methodsCondition.methods.firstOrNull()?.name ?: return@mapNotNull null
            val pattern = info.patternsCondition.patterns.firstOrNull() ?: return@mapNotNull null

            EndPointId(method, pattern)
        }
    }

    fun getListByController(name: String): List<EndPointId> {
        val handlerMethods = requestMappingHandlerMapping.handlerMethods
        return handlerMethods.mapNotNull { (info, method) ->
            if (!method.beanType.name.contains(name)) return@mapNotNull null;

            val method = info.methodsCondition.methods.firstOrNull()?.name ?: return@mapNotNull null
            val pattern = info.patternsCondition.patterns.firstOrNull() ?: return@mapNotNull null

            EndPointId(method, pattern)
        }
    }

    @Transactional
    fun refreshEndPoints() {
        val existingDict = repo.findAll().associateBy { EndPointId(it.method, it.pattern) }

        val handlerMethods = requestMappingHandlerMapping.handlerMethods

        val updateList = mutableListOf<EndPoint>()
        val createList = mutableListOf<EndPoint>()

        for ((info, handler) in handlerMethods) {
            val method = info.methodsCondition.methods.firstOrNull()?.name ?: continue
            val pattern = info.patternsCondition.patterns.firstOrNull() ?: continue
            val name = handler.getMethodAnnotation(ApiOperation::class.java)?.value

            val existing = existingDict[EndPointId(method, pattern)]

            if (existing == null) {
                createList.add(EndPoint(method, pattern, name))
                continue
            }

            if (name != null && existing.name != name) {
                existing.name = name
                updateList.add(existing)
            }
        }

        repo.saveAll(updateList.plus(createList))
    }

    fun getList(userId: Long?): List<EndPoint> {
        if (userId == null) return listOf()

        val endPoints = getQuery(userId).fetch()
        val activeSet = getActiveList().toSet()
        return endPoints.filter { activeSet.contains(EndPointId(it.method, it.pattern)) }
    }

    fun get(userId: Long?, method: String, pattern: String): EndPoint {
        if (userId == null) throw NotFoundException("未找到记录")

        val qEndPoint = QEndPoint.endPoint
        val predicate = qEndPoint.method.eq(method).and(qEndPoint.pattern.eq(pattern))
        val endPoint = getQuery(userId).where(predicate).fetchOne() ?: throw NotFoundException("未找到记录")

        val activeList = getActiveList()
        if (!activeList.any { it.method == endPoint.method && it.pattern == endPoint.pattern }) {
            throw NotFoundException("未找到记录")
        }

        return endPoint
    }

    private fun getQuery(userId: Long): JPAQuery<EndPoint> {
        val qEndPoint = QEndPoint.endPoint
        if (userRepo.superAdmin(userId) == true) {
            return queryFactory.selectFrom(qEndPoint)
        }

        val qUser = QUser.user
        val qGroup = QGroup.group
        val qPermission = QPermission.permission

        return queryFactory
            .select(qEndPoint)
            .from(qUser)
            .leftJoin(qGroup).on(qUser.groups.contains(qGroup))
            .leftJoin(qPermission).on(qGroup.permissions.contains(qPermission))
            .leftJoin(qEndPoint).on(qPermission.endPoints.contains(qEndPoint))
            .where(qUser.id.eq(userId).or(qEndPoint.publicToAll.isTrue))
            .distinct()
    }

    fun hasAccess(userId: Long?, method: String, pattern: String): Boolean {
        if (userId == null) return false

        if (repo.isPublic(method, pattern) == true) return true
        if (userRepo.superAdmin(userId) == true) return true

        val qUser = QUser.user
        val qGroup = QGroup.group
        val qEndPoint = QEndPoint.endPoint
        val qPermission = QPermission.permission

        val predicate = qUser.id.eq(userId)
            .and(qEndPoint.method.eq(method))
            .and(qEndPoint.pattern.eq(pattern))

        val query = queryFactory
            .select(qEndPoint)
            .from(qUser)
            .leftJoin(qUser.groups, qGroup)
            .leftJoin(qGroup.permissions, qPermission)
            .leftJoin(qPermission.endPoints, qEndPoint)
            .where(predicate)

        return query.fetchCount() > 0
    }

    @Transactional
    fun hasAccess(user: User, method: String, pattern: String): Boolean {
        return hasAccess(user.id, method, pattern)
    }
}