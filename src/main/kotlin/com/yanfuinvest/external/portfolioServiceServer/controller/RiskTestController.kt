package com.yanfuinvest.external.portfolioServiceServer.controller

import com.yanfuinvest.common.domain.PaginationRequest
import com.yanfuinvest.common.domain.http.CustomResponse
import com.yanfuinvest.external.portfolioServiceServer.service.RiskTestService
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*

@Validated
@RestController
@RequestMapping("/external/portfolio_service_server/risk_test")
class RiskTestController(
    private val service: RiskTestService
) {
    @GetMapping("list")
    fun getList(
        @RequestParam page: Int,
        @RequestParam pageSize: Int,
        @RequestParam(required = false) userId: Int?,
    ): CustomResponse<List<Any>> {
        val (result, amount) = service.getList(PaginationRequest(page, pageSize), userId)
        return CustomResponse.Ok(data = result, amount = amount)
    }

    @GetMapping("list/{identityNumber}")
    fun getListByIdentityNumber(@PathVariable identityNumber: String) =
        CustomResponse.Ok(service.getListByIdentityNumber(identityNumber))

    @GetMapping("{id}")
    fun getById(@PathVariable id: Int) = CustomResponse.Ok(service.getById(id))

    @GetMapping("questionnaire_link/{id}")
    fun getQuestionnaireById(@PathVariable id: Int) = CustomResponse.Ok(service.getQuestionnaireById(id))
}