package com.yanfuinvest.external.portfolioServiceServer.controller

import com.yanfuinvest.account.domain.User
import com.yanfuinvest.common.domain.http.CustomResponse
import com.yanfuinvest.external.portfolioServiceServer.service.SupplementToCheckService
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*

@Validated
@RestController
@RequestMapping("/external/portfolio_service_server/supplement_to_check")
class SupplementToCheckController(
    private val service: SupplementToCheckService
) {
    @GetMapping("list")
    fun getList() = CustomResponse.Ok(service.getList())

    @GetMapping("alt_list")
    fun getAltList() = CustomResponse.Ok(service.getAltList())

    @PostMapping("create")
    fun create(@AuthenticationPrincipal user: User, @RequestBody data: Map<String, Any?>) =
        CustomResponse.Ok(service.create(user, data))

    @PostMapping("create_alt")
    fun createAlt(@AuthenticationPrincipal user: User, @RequestBody data: Map<String, Any?>) =
        CustomResponse.Ok(service.createAlt(user, data))
}