package com.yanfuinvest.dot.controller

import com.yanfuinvest.common.domain.PaginationRequest
import com.yanfuinvest.common.domain.http.CustomResponse
import com.yanfuinvest.dot.domain.PortfolioInfo
import com.yanfuinvest.dot.domain.dto.PortfolioInfoFilter
import com.yanfuinvest.dot.domain.dto.PortfolioInfoWriteDTO
import com.yanfuinvest.dot.domain.enums.PortfolioInfoStatusEnum
import com.yanfuinvest.dot.domain.enums.PortfolioInfoTypeEnum
import com.yanfuinvest.dot.service.PortfolioInfoService
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*

@Validated
@RestController
@RequestMapping("/dot/portfolio_info")
class PortfolioInfoController(
    private val service: PortfolioInfoService,
) {
    @GetMapping("")
    fun getList(
        @RequestParam page: Int,
        @RequestParam pageSize: Int,
        @RequestParam(required = false) pfName: String?,
        @RequestParam(required = false) pfCode: String?,
        @RequestParam(required = false) status: PortfolioInfoStatusEnum?,
        @RequestParam(required = false) type: PortfolioInfoTypeEnum?,
        @RequestParam(required = false) extra: String?,
    ): CustomResponse<List<PortfolioInfo>> {
        val pagination = PaginationRequest(page, pageSize)
        val filter = PortfolioInfoFilter(pfName, pfCode, status, type, extra)

        val (data, amount) = service.getList(pagination, filter)
        return CustomResponse.Ok(data = data, amount = amount)
    }

    @GetMapping("{id}")
    fun getById(@PathVariable("id") id: Int) = CustomResponse.Ok(service.getById(id))

    @PostMapping("")
    fun create(@RequestBody data: PortfolioInfoWriteDTO) = CustomResponse.Ok(service.create(data))

    @PutMapping("{id}")
    fun update(
        @PathVariable id: Int,
        @RequestBody data: PortfolioInfoWriteDTO,
    ) = CustomResponse.Ok(service.update(id, data))

    @DeleteMapping("{id}")
    fun delete(@PathVariable id: Int) = CustomResponse.Ok(service.delete(id))
}