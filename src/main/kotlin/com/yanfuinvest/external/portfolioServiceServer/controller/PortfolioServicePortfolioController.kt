package com.yanfuinvest.external.portfolioServiceServer.controller

import com.yanfuinvest.common.domain.http.CustomResponse
import com.yanfuinvest.external.portfolioServiceServer.service.PortfolioServicePortfolioService
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@Validated
@RestController
@RequestMapping("/external/portfolio_service_server/portfolio")
class PortfolioServicePortfolioController(
    private val service: PortfolioServicePortfolioService
) {
    @GetMapping("base_list")
    fun getBaseList(): CustomResponse<Any?> {
        return CustomResponse.Ok(service.getBaseList())
    }
}