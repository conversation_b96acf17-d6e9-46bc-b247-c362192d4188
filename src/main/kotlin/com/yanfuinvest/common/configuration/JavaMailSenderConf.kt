package com.yanfuinvest.common.configuration

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.mail.javamail.JavaMailSender
import org.springframework.mail.javamail.JavaMailSenderImpl

@Configuration
class JavaMailSenderConf {

    @Bean
    @Primary
    @ConfigurationProperties(prefix = "spring.mail.primary")
    fun javaMailSender(): JavaMailSender {
        return JavaMailSenderImpl()
    }

    @Bean
    @ConfigurationProperties(prefix = "spring.mail.secondary")
    fun secondarySender(): JavaMailSender {
        return JavaMailSenderImpl()
    }
}