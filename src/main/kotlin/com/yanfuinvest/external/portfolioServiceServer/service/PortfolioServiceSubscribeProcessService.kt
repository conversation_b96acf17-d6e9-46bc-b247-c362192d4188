package com.yanfuinvest.external.portfolioServiceServer.service

import com.yanfuinvest.account.domain.User
import com.yanfuinvest.common.domain.PaginationRequest
import com.yanfuinvest.external.portfolioServiceServer.domain.PortfolioServiceServerResponseCustom
import com.yanfuinvest.external.portfolioServiceServer.domain.dto.SubscribeProcessCreateResult
import com.yanfuinvest.external.portfolioServiceServer.utils.PortfolioServiceServerUtils
import com.yanfuinvest.operation.domain.dto.PortfolioClientDTO
import com.yanfuinvest.operation.domain.dto.PortfolioSubscribeDirectStartDTO
import com.yanfuinvest.operation.repository.PortfolioRepository
import com.yanfuinvest.operation.service.PortfolioClientService
import com.yanfuinvest.operation.service.PortfolioSubscribeDirectService
import com.yanfuinvest.operation.service.PortfolioSubscribeOwnEmpDirectService
import com.yanfuinvest.operation.service.SubscriberService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.core.ParameterizedTypeReference
import org.springframework.http.HttpMethod
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.sql.Date
import java.time.LocalDate
import java.time.format.DateTimeFormatter

@Service
class PortfolioServiceSubscribeProcessService(
    private val prefix: String = "subscribeProcess",
    private val ownSubscriberService: PortfolioSubscribeOwnEmpDirectService
) : PortfolioServiceServerServiceBase<Any>() {
    @Autowired
    private lateinit var subscribeService: PortfolioSubscribeDirectService

    @Autowired
    private lateinit var portfolioRepository: PortfolioRepository

    @Autowired
    private lateinit var portfolioClientService: PortfolioClientService

    override val getListPath = "$prefix.list"
    private val responseType = object : ParameterizedTypeReference<PortfolioServiceServerResponseCustom<Any>>() {}

    private val createResponseType =
        object : ParameterizedTypeReference<PortfolioServiceServerResponseCustom<SubscribeProcessCreateResult>>() {}

    fun getList(
        paginationRequest: PaginationRequest,
        filter: Map<String, Any?>?
    ): Pair<List<Any>, Long> {
        val paginationInput = PortfolioServiceServerUtils.parsePagination(paginationRequest)
        val input = paginationInput.plus(filter ?: emptyMap())

        return getList(input)
    }

    fun getById(id: Long): Any? {
        val input = mapOf("id" to id)
        val result = request("$prefix.byId", HttpMethod.GET, input, responseType)

        return result?.result?.data
    }

    fun getListByIdentityNumber(identityNumber: String): Any? {
        val input = mapOf("identityNumber" to identityNumber)
        val result = request("$prefix.listByIdentityNumber", HttpMethod.GET, input, responseType)

        return result?.result?.data
    }

    fun getDocumentsById(id: Int): Any? {
        val input = mapOf("id" to id)
        return request("$prefix.documentsById", HttpMethod.GET, input)
    }

    fun getContractAndRiskRevealLinkById(id: Int): Any? {
        val input = mapOf("id" to id)
        return request("$prefix.contractAndRiskRevealLinkById", HttpMethod.GET, input)
    }

    @Transactional
    fun create(user: User, data: Map<String, Any?>) {
        val input = getOperatorMap(user).plus(data)

        val isInternalEmployee = input["isInternalEmployee"] as Boolean?

        val subscriptionType = data["type"]?.toString()

        val structuredPortfolioId = (data["structuredPortfolioId"] as? Int)?.toLong()

        if (isInternalEmployee == true){
              val response = request("$prefix.create", HttpMethod.POST, input, createResponseType)
              val result = response?.result?.data?: return

              val client  = portfolioClientService.getOrCreate(PortfolioClientDTO(result))
              val structuredParent = portfolioRepository.findStructuredParent(result.portfolioId)
              val openDate = LocalDate.parse(result.time,DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))

            val ownEmpSubscribeDirect = subscribeService.startInternalEmployeeProcess(user, PortfolioSubscribeDirectStartDTO(
                 portfolioId = structuredParent?.id ?: result.portfolioId,
                 structuredPortfolioId = structuredPortfolioId,
                 clientId = client.id!!,
                 amount = result.amount,
                 openDate = Date.valueOf(openDate),
                 digitalSign = true,
                 subscription = subscriptionType == "认购" ,
                 digitalSignId = result.id,
             ))
            ownSubscriberService.save(ownEmpSubscribeDirect)
        }else{
            val response = request("$prefix.create", HttpMethod.POST, input, createResponseType)
            val result = response?.result?.data ?: return

            val syncTradeProcess = data["syncTradeProcess"] as Boolean?
            if (syncTradeProcess != true) return

            val client = portfolioClientService.getOrCreate(PortfolioClientDTO(result))
            val structuredParent = portfolioRepository.findStructuredParent(result.portfolioId)
            val openDate = LocalDate.parse(result.time, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
            subscribeService.startProcess(
                user, PortfolioSubscribeDirectStartDTO(
                    portfolioId = structuredParent?.id ?: result.portfolioId,
                    structuredPortfolioId = structuredPortfolioId,
                    clientId = client.id!!,
                    amount = result.amount,
                    openDate = Date.valueOf(openDate),
                    digitalSign = true,
                    subscription = subscriptionType == "认购",
                    digitalSignId = result.id,
                )
            )
        }
//        val response = request("$prefix.create", HttpMethod.POST, input, createResponseType)
//        val result = response?.result?.data ?: return
//
//        val syncTradeProcess = data["syncTradeProcess"] as Boolean?
//        if (syncTradeProcess != true) return
//
//        val client = portfolioClientService.getOrCreate(PortfolioClientDTO(result))
//        val structuredParent = portfolioRepository.findStructuredParent(result.portfolioId)
//        val openDate = LocalDate.parse(result.time, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
//        subscribeService.startProcess(
//            user, PortfolioSubscribeDirectStartDTO(
//                portfolioId = structuredParent?.id ?: result.portfolioId,
//                structuredPortfolioId = if (structuredParent != null) result.portfolioId else null,
//                clientId = client.id!!,
//                amount = result.amount,
//                openDate = Date.valueOf(openDate),
//                digitalSign = true,
//                subscription = false,
//                digitalSignId = result.id,
//            )
//        )
    }

    fun review(user: User, path: String, data: Map<String, Any?>) {
        val input = getOperatorMap(user).plus(data)
        request("$prefix.$path", HttpMethod.POST, input)
    }

    fun terminate(user: User, data: Map<String, Any?>) {
        val input = getOperatorMap(user).plus(data)
        request("$prefix.terminate", HttpMethod.POST, input)

        val id = input["id"]?.toString()?.toLongOrNull() ?: return
        try {
            subscribeService.terminateByDigitalSignId(user, id)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun getOperatorMap(user: User): Map<String, Long?> {
        return mapOf("operatorId" to user.id)
    }
}