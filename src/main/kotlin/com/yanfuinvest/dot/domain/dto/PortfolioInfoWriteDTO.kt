package com.yanfuinvest.dot.domain.dto

import com.yanfuinvest.common.annotation.NoArg
import com.yanfuinvest.dot.domain.enums.PortfolioInfoStatusEnum
import com.yanfuinvest.dot.domain.enums.PortfolioInfoTypeEnum

@NoArg
class PortfolioInfoWriteDTO(
    val pfCode: String?,
    val pfName: String?,
    val status: PortfolioInfoStatusEnum?,
    val type: PortfolioInfoTypeEnum?,
    val valid: Boolean?,
    val extra: String?,
)