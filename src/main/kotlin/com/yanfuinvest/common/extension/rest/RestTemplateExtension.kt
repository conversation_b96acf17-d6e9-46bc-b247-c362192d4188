package com.yanfuinvest.common.extension.rest

import com.yanfuinvest.common.domain.exception.BadRequestException
import com.yanfuinvest.common.domain.exception.InvalidParamsException
import com.yanfuinvest.common.domain.exception.NotFoundException
import org.springframework.http.HttpStatus
import org.springframework.web.client.HttpStatusCodeException
import org.springframework.web.client.RestTemplate


fun <T> RestTemplate.execute(block: RestTemplate.() -> T): T {
    try {
        return block()
    } catch (e: HttpStatusCodeException) {
        val message = e.message ?: "未知错误"
        when (e.statusCode) {
            HttpStatus.BAD_REQUEST -> throw BadRequestException(message)
            HttpStatus.FORBIDDEN -> throw InvalidParamsException(message)
            HttpStatus.NOT_FOUND -> throw NotFoundException(message)
            else -> throw e
        }
    }
}