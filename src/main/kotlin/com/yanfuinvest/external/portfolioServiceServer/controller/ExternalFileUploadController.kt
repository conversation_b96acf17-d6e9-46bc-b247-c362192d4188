package com.yanfuinvest.external.portfolioServiceServer.controller

import com.yanfuinvest.common.domain.http.CustomResponse
import com.yanfuinvest.external.portfolioServiceServer.service.ExternalFileUploadService
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestPart
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.multipart.MultipartFile

@Validated
@RestController
@RequestMapping("/external/portfolio_service_server/file_upload")
class ExternalFileUploadController(private val service: ExternalFileUploadService) {
    @PostMapping("")
    fun upload(@RequestPart file: MultipartFile) = CustomResponse.Ok(service.upload(file))
}