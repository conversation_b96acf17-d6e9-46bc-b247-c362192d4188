package com.yanfuinvest.common.utils

import com.yanfuinvest.common.domain.http.CustomResponse
import org.springframework.expression.spel.support.StandardEvaluationContext
import kotlin.reflect.jvm.javaMethod

object SpelFunctions {
    const val CUSTOM_RESPONSE_WITH_DATA = "customResponseWithData"

    @JvmStatic
    fun customResponseWithData(response: CustomResponse<*>, data: Any): CustomResponse<*> {
        return CustomResponse(
            code = response.code,
            message = response.message,
            data = data,
            amount = response.amount,
        )
    }

    fun registerAll(context: StandardEvaluationContext) {
        context.registerFunction(CUSTOM_RESPONSE_WITH_DATA, ::customResponseWithData.javaMethod!!)
    }
}