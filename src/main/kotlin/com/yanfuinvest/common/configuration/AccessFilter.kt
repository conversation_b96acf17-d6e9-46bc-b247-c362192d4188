package com.yanfuinvest.common.configuration

import com.yanfuinvest.account.domain.FilterRule
import com.yanfuinvest.account.domain.RecordFilterRule
import com.yanfuinvest.account.domain.User
import com.yanfuinvest.account.domain.dto.PermissionReadDTO
import com.yanfuinvest.account.domain.traits.IFilterRule
import com.yanfuinvest.account.repository.UserRepository
import com.yanfuinvest.account.service.PermissionService
import com.yanfuinvest.common.controller.RestControllerResponseAdvice
import com.yanfuinvest.common.extension.servelet.getEndPointId
import com.yanfuinvest.common.utils.CustomPropertyWriter
import com.yanfuinvest.meta.repository.EndPointRepository
import com.yanfuinvest.meta.service.EndPointService
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.core.annotation.Order
import org.springframework.data.repository.findByIdOrNull
import org.springframework.security.access.AccessDeniedException
import org.springframework.security.authentication.AccountExpiredException
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Component
import org.springframework.web.filter.OncePerRequestFilter
import org.springframework.web.servlet.HandlerExceptionResolver
import javax.servlet.FilterChain
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse

@Component
@Order(3)
class AccessFilter(
    private val endPointService: EndPointService,
    private val endPointRepo: EndPointRepository,
    private val permissionService: PermissionService,
    private val userRepo: UserRepository,
    @Qualifier("handlerExceptionResolver")
    private val resolver: HandlerExceptionResolver,
): OncePerRequestFilter() {
    override fun doFilterInternal(request: HttpServletRequest, response: HttpServletResponse, chain: FilterChain) {
        try {
            checkAccess(request)
        } catch (e: Exception) {
            when (e) {
                is AccessDeniedException, is AccountExpiredException -> {
                    resolver.resolveException(request, response, null, e)
                    return
                }
                else -> throw e
            }
        }

        fetchFilter(request)
        chain.doFilter(request, response)
    }

    private fun checkAccess(request: HttpServletRequest) {
        val authenticationException = AccountExpiredException("请重新登录")

        val endPointId = request.getEndPointId() ?: throw AccessDeniedException("不允许访问")

        val user = SecurityContextHolder.getContext().authentication.principal

        if (user !is User) {
            val isLoginRequest = endPointId.method == "POST" && endPointId.pattern.startsWith("/account/login")
            if (isLoginRequest) return else throw authenticationException
        }

        val hasAccess = endPointService.hasAccess(user, endPointId.method, endPointId.pattern)
        if (hasAccess) return

        val endPoint = endPointRepo.findByIdOrNull(endPointId)
        throw AccessDeniedException("不允许访问 ${endPoint?.name ?: endPoint?.pattern ?: ""}")
    }

    private fun fetchFilter(request: HttpServletRequest) {
        val user = SecurityContextHolder.getContext().authentication.principal
        if (user !is User) return

        val userId = user.id ?: return
        if (userRepo.superAdmin(userId) == true) return

        val endPointId = request.getEndPointId() ?: return

        val permissions = permissionService.getList(userId)

        fun <T: IFilterRule> search(permission: PermissionReadDTO, rules: Set<T>): List<T> {
            return rules.mapNotNull { filter ->
                if (!filter.endPoint.isSame(endPointId)) return@mapNotNull null

                val restPermissions = permissions.filter { it.id != permission.id }

                val notInRestPermissions = restPermissions.any {
                    val endPointMatch = it.endPoints.any { endPoint -> endPoint.isSame(filter.endPoint) }

                    val filterRules = when (filter) {
                        is FilterRule -> it.filterRules
                        is RecordFilterRule -> it.recordFilterRules
                        else -> listOf()
                    }

                    val filterMatch = filterRules.any { innerFilter -> innerFilter.isSame(filter) }

                    endPointMatch && !filterMatch
                }

                if (notInRestPermissions) null else filter
            }
        }

        val filters = permissions.flatMap { search(it, it.filterRules) }.distinctBy { it.distinctKey }
        val filterExpression = IFilterRule.toExpression(filters)

        filterExpression?.let {
            request.setAttribute(CustomPropertyWriter.FILTER_NAME, filterExpression)
        }

        val recordFilters = permissions.flatMap { search(it, it.recordFilterRules) }.distinctBy { it.distinctKey }
        val recordFilterExpression = IFilterRule.toExpression(recordFilters)

        recordFilterExpression?.let {
            request.setAttribute(RestControllerResponseAdvice.FILTER_NAME, recordFilterExpression)
        }
    }
}