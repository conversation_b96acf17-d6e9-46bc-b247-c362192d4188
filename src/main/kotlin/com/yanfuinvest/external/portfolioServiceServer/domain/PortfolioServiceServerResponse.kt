package com.yanfuinvest.external.portfolioServiceServer.domain

import com.yanfuinvest.common.annotation.NoArg


@NoArg
open class PortfolioServiceServerResponse<T>(
    val result: PortfolioServiceServerResponseBodyInner<PortfolioServiceServerResponseBodySingleInner<T>>? = null,
)

@NoArg
open class PortfolioServiceServerResponseList<T>(
    val result: PortfolioServiceServerResponseBodyInner<PortfolioServiceServerResponseBodyMultiInner<T>>? = null
)

@NoArg
open class PortfolioServiceServerResponseCustom<T>(
    val result: PortfolioServiceServerResponseBodyInner<T>? = null
)

@NoArg
class PortfolioServiceServerResponseBodyInner<T>(
    val data: T,
)

@NoArg
class PortfolioServiceServerResponseBodySingleInner<T>(
    val item: T,
)

@NoArg
class PortfolioServiceServerResponseBodyMultiInner<T>(
    val items: List<T>,
    val amount: Long,
 )


