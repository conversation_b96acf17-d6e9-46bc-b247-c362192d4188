package com.yanfuinvest.external.portfolioServiceServer.service

import com.yanfuinvest.external.portfolioServiceServer.domain.PortfolioServiceServerResponseCustom
import org.springframework.core.ParameterizedTypeReference
import org.springframework.http.HttpMethod
import org.springframework.stereotype.Service

@Service
class PortfolioServicePortfolioService(
    private val prefix: String = "portfolio"
): PortfolioServiceServerServiceBase<Any>() {
    private val baseListResponseType =
        object : ParameterizedTypeReference<PortfolioServiceServerResponseCustom<Any>>() {}

    fun getBaseList(): Any? {
        val result = request("$prefix.baseList", HttpMethod.GET, null, baseListResponseType)
        return result?.result?.data
    }
}