package com.yanfuinvest.external.portfolioServiceServer.controller

import com.yanfuinvest.common.domain.http.CustomResponse
import com.yanfuinvest.external.portfolioServiceServer.service.VideoRecordLegacyService
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@Validated
@RestController
@RequestMapping("/external/portfolio_service_server/video_record_legacy")
class VideoRecordLegacyController(
    private val service: VideoRecordLegacyService
) {
    @GetMapping("")
    fun getById(
        @RequestParam(required = false) userId: Int?,
        @RequestParam(required = false) identityNumber: String?,
        @RequestParam(required = false) portfolioId: Long?,
    ) = CustomResponse.Ok(service.getList(userId, identityNumber, portfolioId))
}