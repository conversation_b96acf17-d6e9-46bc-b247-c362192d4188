package com.yanfuinvest.log.domain

import com.yanfuinvest.common.utils.jpaConvertor.JpaConvertorJson
import org.hibernate.annotations.Immutable
import java.sql.Timestamp
import javax.persistence.*

@Entity
@Immutable
class RequestLog(
    var datetime: Timestamp? = null,

    var userId: Long? = null,

    var method: String? = null,

    var path: String? = null,

    var page: String? = null,

    @Lob
    @Convert(converter = JpaConvertorJson::class)
    var params: Map<String, Array<String>>? = null,

    var code: Int? = null,

    var timecosts: Double? = null,

    @Lob
    var useragent: String? = null,

    @Lob
    var body: String? = null,

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,
)
