package com.yanfuinvest.account.service

import com.yanfuinvest.account.domain.QUser
import com.yanfuinvest.account.domain.User
import com.yanfuinvest.account.domain.dto.UserEditingDTO
import com.yanfuinvest.account.repository.UserRepository
import com.yanfuinvest.common.configuration.Providers
import com.yanfuinvest.common.domain.exception.InvalidParamsException
import com.querydsl.core.types.dsl.BooleanExpression
import com.querydsl.jpa.impl.JPAQueryFactory
import com.yanfuinvest.account.repository.RoleRepository
import com.yanfuinvest.common.domain.constant.Perms
import com.yanfuinvest.common.domain.exception.NotFoundException
import com.yanfuinvest.operation.domain.dto.QUserDTOMin
import com.yanfuinvest.operation.domain.dto.UserDTOMin
import org.springframework.data.repository.findByIdOrNull
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.core.Authentication
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.core.userdetails.UserDetailsService
import org.springframework.security.core.userdetails.UsernameNotFoundException
import org.springframework.security.web.context.HttpSessionSecurityContextRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import javax.servlet.http.HttpServletRequest
import kotlin.reflect.full.memberProperties

@Service
@Transactional(readOnly = true)
class UserService (
    private val userRepo: UserRepository,
    private val roleRepo: RoleRepository,
    private val providers: Providers,
    private val perms: Perms,
    private val queryFactory: JPAQueryFactory,
): UserDetailsService {
    private val q = QUser.user

    override fun loadUserByUsername(username: String) =
        userRepo.findByUsername(username) ?: throw UsernameNotFoundException("User not exists.")

    fun getList(): List<User> = userRepo.findAll()

    fun findById(id: Long) = userRepo.findByIdOrNull(id)

    fun getAllPerm(perm: Perms.PermDetail): List<Perms.PermDetail> {
        val result = mutableListOf<Perms.PermDetail>();
        var curPerm: Perms.PermDetail? = perm
        while (curPerm != null) {
            result += curPerm
            curPerm = curPerm.parent
        }
        return result
    }

    private val permObjects = Perms::class.memberProperties.mapNotNull {
        val value = it.get(perms)
        if (value is Perms.PermDetail) {
            value
        } else {
            null
        }
    }

    fun getAllPerm(permName: String): List<Perms.PermDetail> {
        val found = permObjects.find { it.name == permName }
        return if (found != null)
            getAllPerm(found)
        else
            listOf()
    }

    fun getSubPerm(user: User): List<Perms.PermDetail> {
        return permObjects.filter { getAllPerm(it).map{ perm -> perm.name }.intersect(user.authorities.map { auth -> auth.authority }).isNotEmpty() }
    }

    @Transactional(readOnly = true)
    fun findByPerm(perm: Perms.PermDetail): List<User> {
        val permNames = getAllPerm(perm).map { it.name }
        val roles = roleRepo.findAll().filter { it.permList.intersect(permNames).isNotEmpty() }
        return userRepo.findByRoleIds(roles.map{ it.id!! })
    }

    private fun isValid(): BooleanExpression {
        val user = QUser.user
        return user.isValid.isTrue
    }

    fun getValidList(perms: List<String>): List<User> {
        return if (perms.isEmpty()) {
            userRepo.findAllValid()
        } else {
            val permNames = perms.flatMap { getAllPerm(it) }.map { it.name }.distinct()
            val roles = roleRepo.findAll().filter { it.permList.intersect(permNames).isNotEmpty() }
            userRepo.findByRoleIds(roles.map { it.id!! })
        }
    }

    fun reAuth(auth: Authentication, request: HttpServletRequest) {
        val sc = SecurityContextHolder.getContext()
        sc.authentication = auth
        val session = request.getSession(true)
        session.setAttribute(HttpSessionSecurityContextRepository.SPRING_SECURITY_CONTEXT_KEY, sc)
    }

    @Transactional
    fun addUser(username: String, password: String, isValid: Boolean): User {
        if (userRepo.existsByUsername(username)) {
            throw InvalidParamsException("用户名已存在")
        }
        val newPwd = providers.pwdEncoder.encode(password)
        val user = User(
                username = username,
                password = newPwd,
                pwdResetRequired = true,
                isValid = isValid
        )
        userRepo.saveAndFlush(user)
        return user
    }
    fun getRealNameFromOpenId(openId: String):String?{
        val user = userRepo.findByOpenId(openId)
        return user?.realName ?: user?.username
    }
    @Transactional
    fun addUserFeishu(username: String, openId: String, isValid: Boolean): User {
        if (userRepo.findByOpenId(openId) != null) {
            throw InvalidParamsException("重复的飞书用户")
        }

        if (userRepo.existsByUsername(username)) {
            throw InvalidParamsException("用户名已存在")
        }

        val user = User(
            username = username,
            openId = openId,
            pwdResetRequired = false,
            isValid = isValid
        )
        userRepo.saveAndFlush(user)
        return user
    }

    @Transactional
    fun changePwd(request: HttpServletRequest, user: User, oldPwd: String, newPwd: String) {
        if (!providers.pwdEncoder.matches(oldPwd, user.password)) {
            throw InvalidParamsException("原密码不正确")
        } else {
            user.password = providers.pwdEncoder.encode(newPwd)
            user.pwdResetRequired = false
            userRepo.saveAndFlush(user)
            val auth = SecurityContextHolder.getContext().authentication
            val newAuth = UsernamePasswordAuthenticationToken(user, auth.credentials, auth.authorities)
            reAuth(newAuth, request)
        }
    }

    @Transactional
    fun changePwdByAdmin(id: Long, newPwd: String) {
        val user = userRepo.findByIdOrNull(id)
        if (user == null) {
            throw NotFoundException("用户不存在")
        } else {
            user.password = providers.pwdEncoder.encode(newPwd)
            user.pwdResetRequired = true
            userRepo.saveAndFlush(user)
        }
    }

    @Transactional
    fun editProfile(request: HttpServletRequest, user: User, profile: UserEditingDTO) {
        UserEditingDTO.copier.copy(profile, user, null)
        userRepo.saveAndFlush(user)
        val auth = SecurityContextHolder.getContext().authentication
        val newAuth = UsernamePasswordAuthenticationToken(user, auth.credentials, auth.authorities)
        reAuth(newAuth, request)
    }

    @Transactional
    fun editProfile(userId: Long, profile: UserEditingDTO): User {
        val user = userRepo.findByIdOrNull(userId)
        if (user == null) {
            throw NotFoundException("用户不存在")
        } else {
            UserEditingDTO.copier.copy(profile, user, null)
            userRepo.saveAndFlush(user)
            return user
        }
    }

    @Transactional
    fun setRoles(userId: Long, roleIds: List<Long>): User {
        if (userId == 1L) {
            throw InvalidParamsException("禁止修改admin的角色")
        }
        val user = userRepo.findByIdOrNull(userId)
        val roles = roleRepo.findAllById(roleIds)
        if (user == null) {
            throw NotFoundException("用户不存在")
        } else {
            user.roles.clear()
            user.roles.addAll(roles)
            userRepo.saveAndFlush(user)
            return user
        }
    }

    @Transactional
    fun setValid(userId: Long, valid: Boolean): User {
        if (userId == 1L) {
            throw InvalidParamsException("禁止反激活admin")
        }
        val user = userRepo.findByIdOrNull(userId)
        if (user == null) {
            throw NotFoundException("用户不存在")
        } else {
            user.isValid = valid
            userRepo.saveAndFlush(user)
            return user
        }
    }

    fun getListMin(): List<UserDTOMin> {
        return queryFactory
            .select(QUserDTOMin(q.id, q.realName, q.username))
            .from(q)
            .fetch()
    }

    fun getSuperAdmin(): List<User> {
        return queryFactory.selectFrom(q).where(q.superAdmin.isTrue).fetch()
    }
}
