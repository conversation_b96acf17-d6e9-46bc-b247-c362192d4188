package com.yanfuinvest.dot.domain

import com.yanfuinvest.dot.domain.enums.PortfolioInfoStatusEnum
import com.yanfuinvest.dot.domain.enums.PortfolioInfoTypeEnum
import com.yanfuinvest.dot.utils.PortfolioInfoStatusConverter
import com.yanfuinvest.dot.utils.PortfolioInfoTypeConverter
import javax.persistence.*

@Entity
@Table(name = "dot_portfolio_info", indexes = [Index(columnList = "pf_code")])
class PortfolioInfo(
    @Column(name = "_id")
    val legacyId: String? = null,

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Int? = null,

    @Column(name = "pf_code")
    var pfCode: String? = null,

    var pfName: String? = null,

    @Convert(converter = PortfolioInfoStatusConverter::class)
    var status: PortfolioInfoStatusEnum? = null,

    @Convert(converter = PortfolioInfoTypeConverter::class)
    var type: PortfolioInfoTypeEnum? = null,

    var valid: Boolean? = null,

    var extra: String? = null,
)