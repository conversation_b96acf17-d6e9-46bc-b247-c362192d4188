package com.yanfuinvest.external.portfolioServiceServer.service

import org.springframework.http.HttpMethod
import org.springframework.stereotype.Service

@Service
class NotificationService(
    private val  prefix: String = "notification"
) :PortfolioServiceServerServiceBase<Any>(){

    fun  genEvalExpireInfo(identityNumber: String){
        val input = mapOf("identityNumber" to identityNumber)
        request("$prefix.createByOpeartionAssessment", HttpMethod.POST, input)
    }


    fun createCertExpiryNotice(identityNumber: String){
        val input = mapOf("identityNumber" to identityNumber)
        request("$prefix.createCertExpiryNotice", HttpMethod.POST, input)
    }
}