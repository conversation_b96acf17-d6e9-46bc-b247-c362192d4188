package com.yanfuinvest.external.portfolioServiceServer.controller

import com.yanfuinvest.common.domain.http.CustomResponse
import com.yanfuinvest.external.portfolioServiceServer.service.VideoConfigService
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@Validated
@RestController
@RequestMapping("/external/portfolio_service_server/video_config")
class VideoConfigController(
    private val service: VideoConfigService
) {
    @GetMapping("video_list")
    fun getVideoList(@RequestParam targetCode: String) =
        CustomResponse.Ok(service.getVideoList(targetCode))
}