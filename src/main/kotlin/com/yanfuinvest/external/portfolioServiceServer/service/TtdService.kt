package com.yanfuinvest.external.portfolioServiceServer.service

import com.yanfuinvest.external.portfolioServiceServer.domain.PortfolioServiceServerResponseCustom
import org.springframework.core.ParameterizedTypeReference
import org.springframework.http.HttpMethod
import org.springframework.stereotype.Service

@Service
class TtdService(
    private val prefix: String = "ttd"
): PortfolioServiceServerServiceBase<Any>() {
    private val productListResponseType =
        object : ParameterizedTypeReference<PortfolioServiceServerResponseCustom<Any>>() {}

    fun getProductList(input: Map<String, Any?>?): Any? {
        val result = request("$prefix.productList", HttpMethod.GET, input, productListResponseType)
        return result?.result?.data
    }
}