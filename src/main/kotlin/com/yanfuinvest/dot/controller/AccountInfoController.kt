package com.yanfuinvest.dot.controller

import com.yanfuinvest.common.domain.http.CustomResponse
import com.yanfuinvest.dot.domain.dto.AccountInfoWriteDTO
import com.yanfuinvest.dot.service.AccountInfoService
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*

@Validated
@RestController
@RequestMapping("/dot/account_info")
class AccountInfoController(
    private val service: AccountInfoService,
) {
    @GetMapping("")
    fun getListByPfCode(@RequestParam pfCode: String) = CustomResponse.Ok(service.getListByPfCode(pfCode))

    @PostMapping("")
    fun create(@RequestBody data: AccountInfoWriteDTO) = CustomResponse.Ok(service.create(data))

    @PutMapping("{id}")
    fun update(
        @PathVariable id: Int,
        @RequestBody data: AccountInfoWriteDTO,
    ) = CustomResponse.Ok(service.update(id, data))

    @DeleteMapping("{id}")
    fun delete(@PathVariable id: Int) = CustomResponse.Ok(service.delete(id))
}