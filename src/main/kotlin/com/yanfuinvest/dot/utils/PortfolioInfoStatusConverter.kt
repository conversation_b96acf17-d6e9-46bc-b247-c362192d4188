package com.yanfuinvest.dot.utils

import com.yanfuinvest.dot.domain.enums.PortfolioInfoStatusEnum
import javax.persistence.AttributeConverter
import javax.persistence.Converter

@Converter
class PortfolioInfoStatusConverter: AttributeConverter<PortfolioInfoStatusEnum, String> {
    override fun convertToDatabaseColumn(attribute: PortfolioInfoStatusEnum?): String? {
        return attribute?.value
    }

    override fun convertToEntityAttribute(data: String?): PortfolioInfoStatusEnum? {
        return PortfolioInfoStatusEnum.values().find { it.value == data }
    }
}