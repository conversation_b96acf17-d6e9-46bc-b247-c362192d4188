package com.yanfuinvest.external.portfolioServiceServer.service

import com.yanfuinvest.account.domain.User
import com.yanfuinvest.external.portfolioServiceServer.domain.PortfolioServiceServerResponseCustom
import org.springframework.core.ParameterizedTypeReference
import org.springframework.http.HttpMethod
import org.springframework.stereotype.Service

@Service
class SupplementToCheckService(
    private val prefix: String = "supplementToCheck"
) : PortfolioServiceServerServiceBase<Any>() {
    override val getListPath = "$prefix.list"

    private val responseType =
        object : ParameterizedTypeReference<PortfolioServiceServerResponseCustom<Any>>() {}

    fun getList(): Any? {
        val result = request(getListPath, HttpMethod.GET, null, responseType)
        return result?.result?.data
    }

    fun getAltList(): Any? {
        val result = request("$prefix.altList", HttpMethod.GET, null, responseType)
        return result?.result?.data
    }

    fun create(user: User, data: Map<String, Any?>): Any? {
        val input = mapOf("operatorId" to user.id).plus(data)
        val result = request("$prefix.create", HttpMethod.POST, input, responseType)
        return result?.result?.data
    }

    fun createAlt(user: User, data: Map<String, Any?>): Any? {
        val input = mapOf("operatorId" to user.id).plus(data)
        val result = request("$prefix.createAlt", HttpMethod.POST, input, responseType)
        return result?.result?.data
    }
}