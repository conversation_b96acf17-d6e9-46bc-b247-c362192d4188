package com.yanfuinvest.common.utils

import org.springframework.transaction.PlatformTransactionManager
import org.springframework.transaction.TransactionDefinition
import org.springframework.transaction.TransactionStatus
import org.springframework.transaction.support.TransactionTemplate
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.temporal.IsoFields
import java.util.*

object Utils {
    val dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日")
    private val simpleDateFormat = SimpleDateFormat("yyyy年MM月dd日")
    private val simpleDateFormatDash = SimpleDateFormat("yyyy-MM-dd")

    val formattedDate: String
        get() = LocalDate.now().format(dateTimeFormatter)

    fun getFormattedDate(date: Date): String =
        simpleDateFormat.format(date)


    fun getDashFormattedDate(date: Date): String =
        simpleDateFormatDash.format(date)

    fun isSameWeek(first: LocalDate, second: LocalDate): Boolean {
        val firstWeekOfYear = first[IsoFields.WEEK_OF_WEEK_BASED_YEAR]
        val secondWeekOfYear = second[IsoFields.WEEK_OF_WEEK_BASED_YEAR]

        return firstWeekOfYear == secondWeekOfYear
    }

    fun isSameMonth(first: LocalDate, second: LocalDate): Boolean =
        first.monthValue == second.monthValue

    fun isSameQuarter(first: LocalDate, second: LocalDate): Boolean {
        val firstQuarterOfYear = first[IsoFields.QUARTER_OF_YEAR]
        val secondQuarterOfYear = second[IsoFields.QUARTER_OF_YEAR]

        return firstQuarterOfYear == secondQuarterOfYear
    }

    fun toHalfWidth(str: String): String {
        return str.map { c ->
            if (c in '！'..'～') {
                c - 0xFEE0
            } else {
                c
            }
        }.joinToString("")
    }

    fun toFullWidth(str: String): String {
        return str.map { c ->
            if (c in '!'..'~') {
                (c + 0xFEE0)
            } else {
                c
            }
        }.joinToString("")
    }

    fun hashCode(vararg items: Any?): Int {
        return items.fold(0) { acc, item -> acc xor item.hashCode() }
    }

    fun executeInNewTransaction(
        transactionManager: PlatformTransactionManager,
        action: (status: TransactionStatus) -> Unit,
    ) {
        val template = TransactionTemplate(transactionManager)
        template.propagationBehavior = TransactionDefinition.PROPAGATION_REQUIRES_NEW
        template.execute(action)
    }
}
