package com.yanfuinvest.common.configuration

import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.databind.BeanDescription
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.SerializationConfig
import com.fasterxml.jackson.databind.deser.std.StdScalarDeserializer
import com.fasterxml.jackson.databind.module.SimpleModule
import com.fasterxml.jackson.databind.ser.BeanPropertyWriter
import com.fasterxml.jackson.databind.ser.BeanSerializerModifier
import com.yanfuinvest.common.utils.CustomPropertyWriter
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.util.StringUtils

@Configuration
class ObjectMapperConf {
    @Bean
    fun jackson2ObjectMapperBuilderCustomizer() =
        Jackson2ObjectMapperBuilderCustomizer { builder ->
            val module = object: SimpleModule() {
                override fun setupModule(context: SetupContext) {
                    super.setupModule(context)
                    context.addBeanSerializerModifier(object: BeanSerializerModifier() {
                        override fun changeProperties(
                            config: SerializationConfig,
                            beanDesc: BeanDescription,
                            beanProperties: List<BeanPropertyWriter>
                        ): List<BeanPropertyWriter> {
                            val properties = beanProperties.map { CustomPropertyWriter(it) }
                            return super.changeProperties(config, beanDesc, properties)
                        }
                    })
                }
            }

            builder.modulesToInstall(module)

            val deserializer = object: StdScalarDeserializer<String>(String::class.java) {
                override fun deserialize(parser: JsonParser, context: DeserializationContext): String {
                    return StringUtils.trimWhitespace(parser.valueAsString)
                }
            }

            builder.deserializerByType(String::class.java, deserializer)
        }
}