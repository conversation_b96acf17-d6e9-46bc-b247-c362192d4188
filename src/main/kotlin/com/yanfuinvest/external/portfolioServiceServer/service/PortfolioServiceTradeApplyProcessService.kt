package com.yanfuinvest.external.portfolioServiceServer.service

import com.yanfuinvest.account.domain.User
import com.yanfuinvest.external.portfolioServiceServer.domain.PortfolioServiceServerResponseCustom
import org.springframework.core.ParameterizedTypeReference
import org.springframework.http.HttpMethod
import org.springframework.stereotype.Service

@Service
class PortfolioServiceTradeApplyProcessService(
    private val prefix: String = "tradeApplyProcess"
): PortfolioServiceServerServiceBase<Any>() {
    private val responseType = object : ParameterizedTypeReference<PortfolioServiceServerResponseCustom<Any>>() {}

    fun getDocumentsById(id: Int): Any? {
        val input = mapOf("id" to id)
        return request("$prefix.documentsById", HttpMethod.GET, input)
    }

    fun getListByIdentityNumber(identityNumber: String): Any? {
        val input = mapOf("identityNumber" to identityNumber)
        val result= request("$prefix.listByIdentityNumber", HttpMethod.GET, input, responseType)

        return result?.result?.data
    }

    fun create(user: User, data: Map<String, Any?>) {
        val input = getOperatorMap(user).plus(data)
        request("$prefix.create", HttpMethod.POST, input)
    }

    fun review(user: User, path: String, data: Map<String, Any?>) {
        val input = getOperatorMap(user).plus(data)
        request("$prefix.$path", HttpMethod.POST, input)
    }

    private fun getOperatorMap(user: User): Map<String, Long?> {
        return mapOf("operatorId" to user.id)
    }
}