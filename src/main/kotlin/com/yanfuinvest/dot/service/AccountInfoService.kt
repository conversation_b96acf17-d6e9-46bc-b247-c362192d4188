package com.yanfuinvest.dot.service

import com.querydsl.core.BooleanBuilder
import com.querydsl.jpa.impl.JPAQueryFactory
import com.yanfuinvest.common.domain.exception.InvalidParamsException
import com.yanfuinvest.common.domain.exception.NotFoundException
import com.yanfuinvest.dot.domain.AccountInfo
import com.yanfuinvest.dot.domain.QAccountInfo
import com.yanfuinvest.dot.domain.dto.AccountInfoWriteDTO
import com.yanfuinvest.dot.repository.AccountInfoRepository
import com.yanfuinvest.dot.repository.PortfolioInfoRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.*
import javax.persistence.LockModeType

@Service
class AccountInfoService(
    private val repo: AccountInfoRepository,
    private val queryFactory: JPAQueryFactory,
    private val portfolioInfoRepo: PortfolioInfoRepository
) {
    private val q = QAccountInfo.accountInfo

    fun getListByPfCode(pfCode: String): List<AccountInfo> {
        val predicate = BooleanBuilder()
            .and(q.pfCode.eq(pfCode))
            .and(q.valid.isNull)

        return queryFactory.selectFrom(q).where(predicate).fetch()
    }

    private fun getForUpdate(id: Int): AccountInfo {
        val query = queryFactory.selectFrom(q).where(q.id.eq(id)).setLockMode(LockModeType.PESSIMISTIC_WRITE)
        return query.fetchOne() ?: throw NotFoundException("未找到记录")
    }

    @Transactional
    fun create(data: AccountInfoWriteDTO) {
        if (!portfolioInfoRepo.existsByPfCode(data.pfCode)) throw NotFoundException("未找到记录")

        val record = AccountInfo(
            pfCode = data.pfCode,
            accountId = data.accountId,
            capitalPwd = data.capitalPwd,
            commission = data.commission,
            company = data.company,
            status = data.status,
            tradingPwd = data.tradingPwd,
            type = data.type,
            valid = data.valid,
            client = data.client,
            extra = data.extra,
            communicationPwd = data.communicationPwd,
            monitorAccount = data.monitorAccount,
        )

        repo.save(record)
    }

    @Transactional
    fun update(id: Int, data: AccountInfoWriteDTO) {
        val record = getForUpdate(id)

        record.accountId = data.accountId
        record.capitalPwd = data.capitalPwd
        record.commission = data.commission
        record.company = data.company
        record.status = data.status
        record.tradingPwd = data.tradingPwd
        record.type = data.type
        record.valid = data.valid
        record.client = data.client
        record.extra = data.extra
        record.communicationPwd = data.communicationPwd
        record.monitorAccount = data.monitorAccount

        repo.save(record)
    }

    @Transactional
    fun delete(id: Int) {
        repo.deleteById(id)
    }
}