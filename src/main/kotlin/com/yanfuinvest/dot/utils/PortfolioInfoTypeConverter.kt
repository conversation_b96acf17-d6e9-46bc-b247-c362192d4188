package com.yanfuinvest.dot.utils

import com.yanfuinvest.dot.domain.enums.PortfolioInfoTypeEnum
import javax.persistence.AttributeConverter
import javax.persistence.Converter

@Converter
class PortfolioInfoTypeConverter: AttributeConverter<PortfolioInfoTypeEnum, String> {
    override fun convertToDatabaseColumn(attribute: PortfolioInfoTypeEnum?): String? {
        return attribute?.value
    }

    override fun convertToEntityAttribute(data: String?): PortfolioInfoTypeEnum? {
        return PortfolioInfoTypeEnum.values().find { it.value == data }
    }
}