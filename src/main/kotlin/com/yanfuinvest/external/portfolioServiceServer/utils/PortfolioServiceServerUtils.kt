package com.yanfuinvest.external.portfolioServiceServer.utils

import com.yanfuinvest.common.domain.PaginationRequest

object PortfolioServiceServerUtils {
    fun parsePagination(paginationRequest: PaginationRequest): Map<String, Any?> {
        return mapOf(
            "page" to paginationRequest.page,
            "pageSize" to paginationRequest.pageSize,
            "sortKey" to paginationRequest.sortKey,
            "sortOrder" to paginationRequest.sortDesc?.let { if (it) "desc" else "asc" }
        )
    }
}