package com.yanfuinvest.common.controller

import com.yanfuinvest.common.domain.exception.BadRequestException
import com.yanfuinvest.common.domain.exception.InvalidParamsException
import com.yanfuinvest.common.domain.exception.NotFoundException
import com.yanfuinvest.common.domain.exception.UnavailableException
import com.yanfuinvest.common.domain.http.CustomResponse
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.AccessDeniedException
import org.springframework.security.core.AuthenticationException
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.RestControllerAdvice
import org.springframework.web.context.request.WebRequest
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler
import java.lang.Exception
import java.lang.RuntimeException

@RestControllerAdvice
class ExceptionController: ResponseEntityExceptionHandler() {
    @ExceptionHandler(InvalidParamsException::class)
    fun handleWrongParamsException(ex: Exception, request: WebRequest) =
        handleExceptionInternal(ex, null, HttpHeaders(), HttpStatus.FORBIDDEN, request)

    @ExceptionHandler(AccessDeniedException::class)
    fun handleAccessDeniedException(ex: Exception, request: WebRequest) =
        handleExceptionInternal(ex, null, HttpHeaders(), HttpStatus.FORBIDDEN, request)

    @ExceptionHandler(NotFoundException::class)
    fun handleNotFoundException(ex: Exception, request: WebRequest) =
        handleExceptionInternal(ex, null, HttpHeaders(), HttpStatus.NOT_FOUND, request)

    @ExceptionHandler(AuthenticationException::class)
    fun handleAuthenticationException(ex: Exception, request: WebRequest) =
        handleExceptionInternal(RuntimeException("请求被拒绝，请重新登录"), null, HttpHeaders(), HttpStatus.UNAUTHORIZED, request)

    @ExceptionHandler(UnavailableException::class)
    fun handleUnavailableException(ex: Exception, request: WebRequest) =
        handleExceptionInternal(ex, null, HttpHeaders(), HttpStatus.SERVICE_UNAVAILABLE, request)

    @ExceptionHandler(BadRequestException::class)
    fun handleBadRequestException(ex: Exception, request: WebRequest) =
        handleExceptionInternal(ex, null, HttpHeaders(), HttpStatus.BAD_REQUEST, request)

    override fun handleExceptionInternal(ex: Exception, body: Any?, headers: HttpHeaders, status: HttpStatus, request: WebRequest): ResponseEntity<Any> {
        return ResponseEntity(CustomResponse(-1, body, ex.message ?: ex.toString()), headers, status)
    }
}
