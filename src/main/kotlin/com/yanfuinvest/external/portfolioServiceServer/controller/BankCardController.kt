package com.yanfuinvest.external.portfolioServiceServer.controller

import com.yanfuinvest.common.domain.PaginationRequest
import com.yanfuinvest.common.domain.http.CustomResponse
import com.yanfuinvest.external.portfolioServiceServer.service.BankCardService
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*

@Validated
@RestController
@RequestMapping("/external/portfolio_service_server/bank_card")
class BankCardController(
    private val service: BankCardService
) {
    @PostMapping("list")
    fun getList(
        @RequestParam page: Int,
        @RequestParam pageSize: Int,
        @RequestBody(required = false) filter: Map<String, Any?>?
    ): CustomResponse<List<Any>> {
        val (result, amount) = service.getList(PaginationRequest(page, pageSize), filter)
        return CustomResponse.Ok(data = result, amount = amount)
    }
}