package com.yanfuinvest.account.service

import com.querydsl.jpa.impl.JPAQuery
import com.querydsl.jpa.impl.JPAQueryFactory
import com.yanfuinvest.account.domain.Permission
import com.yanfuinvest.account.domain.QFilterRule
import com.yanfuinvest.account.domain.QGroup
import com.yanfuinvest.account.domain.QPermission
import com.yanfuinvest.account.domain.QRecordFilterRule
import com.yanfuinvest.account.domain.QUser
import com.yanfuinvest.account.domain.dto.PermissionEndPointWriteDTO
import com.yanfuinvest.account.domain.dto.PermissionReadDTO
import com.yanfuinvest.account.domain.dto.PermissionWriteDTO
import com.yanfuinvest.account.repository.PermissionRepository
import com.yanfuinvest.account.repository.UserRepository
import com.yanfuinvest.common.domain.exception.NotFoundException
import com.yanfuinvest.meta.repository.EndPointRepository
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import javax.persistence.EntityManager

@Service
class PermissionService(
    private val repo: PermissionRepository,
    private val userRepo: UserRepository,
    private val queryFactory: JPAQueryFactory,
    private val endPointRepo: EndPointRepository,
    private val entityManager: EntityManager,
) {
    @Transactional
    fun getList(userId: Long?): List<PermissionReadDTO> {
        if (userId == null) return listOf()

        val permissions = getQuery(userId).fetch()

        return permissions.map {
            PermissionReadDTO(
                it.id,
                it.name,
                it.description,
                it.category,
                it.endPoints,
                it.filterRules,
                it.recordFilterRules,
            )
        }
    }

    fun get(userId: Long?, id: Long): PermissionReadDTO {
        if (userId == null) throw NotFoundException("未找到记录")

        val qPermission = QPermission.permission
        val permission = getQuery(userId).where(qPermission.id.eq(id)).fetchOne() ?: throw NotFoundException("未找到记录")

        return PermissionReadDTO(
            permission.id,
            permission.name,
            permission.description,
            permission.category,
            permission.endPoints,
            permission.filterRules,
            permission.recordFilterRules
        )
    }

    private fun getQuery(userId: Long): JPAQuery<Permission>  {
        val qPermission = QPermission.permission
        val qFilterRule = QFilterRule.filterRule
        val qRecordFilterRule = QRecordFilterRule.recordFilterRule

        if (userRepo.superAdmin(userId) == true) {
            return queryFactory
                .selectFrom(qPermission)
                .leftJoin(qPermission.endPoints).fetchJoin()
                .leftJoin(qPermission.filterRules, qFilterRule).fetchJoin()
                .leftJoin(qFilterRule.endPoint).fetchJoin()
                .leftJoin(qPermission.recordFilterRules, qRecordFilterRule).fetchJoin()
                .leftJoin(qRecordFilterRule.endPoint).fetchJoin()
                .distinct()
        }

        val qUser = QUser.user
        val qGroup = QGroup.group

        return queryFactory
            .select(qPermission)
            .from(qUser)
            .leftJoin(qUser.groups, qGroup)
            .leftJoin(qGroup.permissions, qPermission)
            .leftJoin(qPermission.endPoints).fetchJoin()
            .leftJoin(qPermission.filterRules, qFilterRule).fetchJoin()
            .leftJoin(qFilterRule.endPoint).fetchJoin()
            .leftJoin(qPermission.recordFilterRules, qRecordFilterRule).fetchJoin()
            .leftJoin(qRecordFilterRule.endPoint).fetchJoin()
            .where(qUser.id.eq(userId).and(qPermission.isNotNull))
            .distinct()
    }

    @Transactional
    fun create(data: PermissionWriteDTO): Permission {
        val permission = Permission(name = data.name, description = data.description, category = data.category ?: "未分类")
        return repo.save(permission)
    }

    @Transactional
    fun update(id: Long, data: PermissionWriteDTO): Permission {
        val permission = repo.findByIdOrNull(id) ?: throw NotFoundException("未找到记录")

        permission.name = data.name
        permission.description = data.description
        permission.category = data.category ?: "未分类"

        return repo.save(permission)
    }

    @Transactional
    fun setEndPoints(id: Long, data: PermissionEndPointWriteDTO): Permission {
        val permission = repo.findByIdOrNull(id) ?: throw NotFoundException("未找到记录")

        val endPoints = endPointRepo.findAllById(data.endPointIds)
        permission.endPoints.clear()
        permission.endPoints.addAll(endPoints)

        return repo.save(permission)
    }

    @Transactional
    fun delete(id: Long) {
        entityManager
            .createNativeQuery("delete from group_permissions where permissions_id = $id")
            .executeUpdate()

        entityManager.flush()
        repo.deleteById(id)
    }
}