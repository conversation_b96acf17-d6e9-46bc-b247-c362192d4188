package com.yanfuinvest.external.portfolioServiceServer.domain

import com.alibaba.excel.annotation.ExcelIgnore
import com.alibaba.excel.annotation.ExcelProperty

class PortfolioComplianceConf {
    @ExcelIgnore
    var portfolioId: Long? = null

    @ExcelProperty("设置情况")
    var status: String? = null

    @ExcelProperty("产品名称")
    var portfolioName: String? = null

    @ExcelProperty("产品编号")
    var portfolioNo: String? = null

    @ExcelProperty("成立日")
    var portfolioPublishDate: String? = null

    @ExcelProperty("运行状态")
    var portfolioState: String? = null

    @ExcelProperty("产品查看权限")
    var visibility: String? = null

    @ExcelProperty("收益展示")
    var earningVisibility: String? = null

    @ExcelProperty("展示的产品要素")
    var fieldVisibility: String? = null

    @ExcelProperty("公告权限")
    var reportVisibility: String? = null

    @ExcelProperty("产品收益率")
    var earningRateVisibility: String? = null
}