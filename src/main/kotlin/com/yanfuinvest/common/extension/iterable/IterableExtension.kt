package com.yanfuinvest.common.extension.iterable

fun <T, K>Iterable<T>.associateByKeyNotNull(keySelector: (T) -> K?): Map<K, T> {
    val map = mutableMapOf<K, T>()

    for (element in this) {
        val key = keySelector(element) ?: continue
        map[key] = element
    }

    return map
}

fun <T, K>Iterable<T>.groupByKeyNotNull(keySelector: (T) -> K?): Map<K, List<T>> {
    val map = mutableMapOf<K, MutableList<T>>()

    for (element in this) {
        val key = keySelector(element) ?: continue
        if (map[key] == null) map[key] = mutableListOf()
        map[key]!!.add(element)
    }

    return map
}

