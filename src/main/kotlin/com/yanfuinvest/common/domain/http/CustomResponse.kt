package com.yanfuinvest.common.domain.http

open class CustomResponse<T> (
    val code: Int = 0,
    val data: T,
    val message: String = "",
    val amount: Long? = null
) {
    class Ok<T>(data: T, amount: Long? = null): CustomResponse<T>(data = data, message = "ok", amount = amount)

    fun <T> withData(data: T) =
        CustomResponse(
            code = code,
            data = data,
            message = message,
            amount = amount,
        )
}
