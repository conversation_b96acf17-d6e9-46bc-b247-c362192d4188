package com.yanfuinvest.common.controller

import com.yanfuinvest.common.domain.http.CustomResponse
import com.yanfuinvest.common.utils.SpelFunctions
import org.springframework.core.MethodParameter
import org.springframework.expression.spel.standard.SpelExpressionParser
import org.springframework.expression.spel.support.StandardEvaluationContext
import org.springframework.http.MediaType
import org.springframework.http.converter.HttpMessageConverter
import org.springframework.http.server.ServerHttpRequest
import org.springframework.http.server.ServerHttpResponse
import org.springframework.web.bind.annotation.RestControllerAdvice
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice
import javax.servlet.http.HttpServletRequest

@RestControllerAdvice
class RestControllerResponseAdvice(private val httpServletRequest: HttpServletRequest) : ResponseBodyAdvice<CustomResponse<*>> {
    override fun supports(returnType: MethodParameter, converterType: Class<out HttpMessageConverter<*>>): Boolean {
        val returnClass = returnType.method?.returnType ?: return false
        return listOf(CustomResponse::class, CustomResponse.Ok::class).any { returnClass.isAssignableFrom(it.java) }
    }

    override fun beforeBodyWrite(
        body: CustomResponse<*>?,
        returnType: MethodParameter,
        selectedContentType: MediaType,
        selectedConverterType: Class<out HttpMessageConverter<*>>,
        request: ServerHttpRequest,
        response: ServerHttpResponse
    ): CustomResponse<*>? {
        if (body == null) return null

        return filter(body, httpServletRequest)
    }

    companion object {
        private const val PACKAGE_NAME = "com.yanfuinvest.common.controller.RestControllerResponseAdvice"
        const val FILTER_NAME = "$PACKAGE_NAME.filter"

        fun filter(body: CustomResponse<*>, request: HttpServletRequest): CustomResponse<*> {
            if (body.data == null) return body

            val predicateExpression = request.getAttribute(FILTER_NAME)
            if (predicateExpression !is String || predicateExpression.isEmpty()) return body

            val expression = when (body.data) {
                is Iterable<*>, is Map<*, *> -> "#root.?[!($predicateExpression)]"
                else -> "$predicateExpression ? null : #root"
            }

            val context = StandardEvaluationContext(body.data)
            SpelFunctions.registerAll(context)

            val parser = SpelExpressionParser()
            val result = parser.parseExpression(expression).getValue(context)

            return body.withData(result)
        }
    }
}