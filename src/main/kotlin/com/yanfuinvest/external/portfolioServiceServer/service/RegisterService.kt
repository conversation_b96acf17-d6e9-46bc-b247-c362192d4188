package com.yanfuinvest.external.portfolioServiceServer.service

import com.yanfuinvest.common.domain.PaginationRequest
import com.yanfuinvest.external.portfolioServiceServer.utils.PortfolioServiceServerUtils
import org.springframework.http.HttpMethod
import org.springframework.stereotype.Service

@Service
class RegisterService(
    private val prefix: String = "register"
): PortfolioServiceServerServiceBase<Any>() {
    override val getListPath = "$prefix.list"

    fun getList(
        paginationRequest: PaginationRequest,
        filter: Map<String, Any?>?
    ): Pair<List<Any>, Long> {
        val paginationInput = PortfolioServiceServerUtils.parsePagination(paginationRequest)
        val input = paginationInput.plus(filter ?: emptyMap())

        return getList(input)
    }

    fun generateCode(id: Int) {
        val input = mapOf("id" to id)
        request("$prefix.generateCode", HttpMethod.POST, input)
    }

    fun redoIdentityVerification(id: Int) {
        val input = mapOf("id" to id)
        request("$prefix.redoIdentityVerification", HttpMethod.POST, input)
    }
}

