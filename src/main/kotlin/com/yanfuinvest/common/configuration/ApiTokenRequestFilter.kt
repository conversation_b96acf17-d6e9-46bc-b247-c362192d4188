package com.yanfuinvest.common.configuration

import com.yanfuinvest.account.domain.User
import com.yanfuinvest.account.repository.UserRepository
import com.yanfuinvest.account.repository.ApiTokenRepository
import com.yanfuinvest.common.extension.servelet.getEndPointId
import com.yanfuinvest.common.utils.ApiTokenUtil
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.core.annotation.Order
import org.springframework.security.access.AccessDeniedException
import org.springframework.security.authentication.AccountExpiredException
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Component
import org.springframework.web.filter.OncePerRequestFilter
import org.springframework.web.servlet.HandlerExceptionResolver
import javax.servlet.FilterChain
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse

@Component
@Order(2)
class ApiTokenRequestFilter(
    private val apiTokenUtil: ApiTokenUtil,
    private val apiTokenRepo: ApiTokenRepository,
    private val userRepo: UserRepository,
    @Qualifier("handlerExceptionResolver")
    private val resolver: HandlerExceptionResolver,
): OncePerRequestFilter() {
    override fun doFilterInternal(request: HttpServletRequest, response: HttpServletResponse, chain: FilterChain) {
        var isApiTokenRequset: Boolean

        try {
            isApiTokenRequset = check(request)
        } catch (e: Exception) {
            when (e) {
                is AccountExpiredException, is AccessDeniedException -> {
                    resolver.resolveException(request, response, null, e)
                    return
                }

                else -> throw e
            }
        }

        chain.doFilter(request, response)

        if (isApiTokenRequset) {
            SecurityContextHolder.clearContext()
            request.session.invalidate()
        }
    }

    fun check(request: HttpServletRequest): Boolean {
        if (SecurityContextHolder.getContext().authentication.principal is User) return false

        val endPointId = request.getEndPointId() ?: throw AccessDeniedException("不允许访问")

        val isLoginRequest = endPointId.method == "POST" && endPointId.pattern.startsWith("/account/login")
        if (isLoginRequest) return false

        val header = request.getHeader("Authorization")
        if (header?.startsWith("Bearer ") != true) throw AccountExpiredException("Authorization header is missing")

        val token = header.substring(7)
        val tokenHash = apiTokenUtil.hashToken(token)
        val userToken = apiTokenRepo.getByTokenHash(tokenHash) ?: throw AccessDeniedException("Access denied")

        val user = userRepo.getById(userToken.userId) ?: throw AccessDeniedException("Access denied")
        val auth = UsernamePasswordAuthenticationToken(user, null, user.authorities)

        SecurityContextHolder.getContext().authentication = auth

        return true
    }
}