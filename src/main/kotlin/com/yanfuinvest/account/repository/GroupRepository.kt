package com.yanfuinvest.account.repository

import com.yanfuinvest.account.domain.Group
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface GroupRepository: JpaRepository<Group, Long> {
    fun findByIdAndOwnerId(id: Long, ownerId: Long): Group?

    fun findAllByIdInAndOwnerId(ids: List<Long>, ownerId: Long): List<Group>

    fun deleteByIdAndOwnerId(id: Long, ownerId: Long)
}
