package com.yanfuinvest.dot.domain

import com.yanfuinvest.dot.domain.enums.AccountInfoTypeEnum
import com.yanfuinvest.dot.domain.enums.PortfolioInfoStatusEnum
import com.yanfuinvest.dot.utils.AccountInfoTypeConverter
import com.yanfuinvest.dot.utils.PortfolioInfoStatusConverter
import javax.persistence.*

@Entity
@Table(name = "dot_account_info", indexes = [Index(columnList = "pf_code")])
class AccountInfo(
    @Column(name = "_id")
    val legacyId: String? = null,

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Int? = null,

    @Column(name = "pf_code")
    val pfCode: String,

    var accountId: String,

    var capitalPwd: String? = null,

    var commission: String? = null,

    var company: String? = null,

    @Convert(converter = PortfolioInfoStatusConverter::class)
    var status: PortfolioInfoStatusEnum? = null,

    var tradingPwd: String? = null,

    @Convert(converter = AccountInfoTypeConverter::class)
    var type: AccountInfoTypeEnum? = null,

    var valid: Boolean? = null,

    var client: String? = null,

    var extra: String? = null,

    var communicationPwd: String? = null,

    var monitorAccount: String? = null,
)
