package com.yanfuinvest.meta.controller

import com.yanfuinvest.account.domain.User
import com.yanfuinvest.common.domain.http.CustomResponse
import com.yanfuinvest.meta.service.EndPointService
import io.swagger.annotations.ApiOperation
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@Validated
@RestController
@RequestMapping("/meta/end_point")
class EndPointController(private val service: EndPointService) {
    @GetMapping("")
    fun getList(@AuthenticationPrincipal user: User) =
        CustomResponse.Ok(service.getList(user.id))

    @GetMapping("single")
    fun get(@AuthenticationPrincipal user: User, @RequestParam method: String, @RequestParam pattern: String) =
        CustomResponse.Ok(service.get(user.id, method, pattern))

    @GetMapping("has_access")
    @ApiOperation(value = "检查是否可以访问 API")
    fun hasAccess(@AuthenticationPrincipal user: User, @RequestParam method: String, @RequestParam pattern: String) =
        CustomResponse.Ok(service.hasAccess(user, method, pattern))

    @GetMapping("list_by_controller")
    fun getListByController(@RequestParam name: String) =
        CustomResponse.Ok(service.getListByController(name))
}