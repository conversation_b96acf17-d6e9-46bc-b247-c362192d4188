package com.yanfuinvest.account.service

import com.querydsl.core.BooleanBuilder
import com.querydsl.jpa.impl.JPAQueryFactory
import com.yanfuinvest.account.domain.*
import com.yanfuinvest.account.domain.dto.FilterRuleWriteDTO
import com.yanfuinvest.account.repository.PermissionRepository
import com.yanfuinvest.account.repository.RecordFilterRuleRepository
import com.yanfuinvest.account.repository.UserRepository
import com.yanfuinvest.meta.domain.QEndPoint
import com.yanfuinvest.meta.repository.EndPointRepository
import javassist.NotFoundException
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service

@Service
class RecordFilterRuleService(
    private val repo: RecordFilterRuleRepository,
    private val queryFactory: JPAQueryFactory,
    private val userRepo: UserRepository,
    private val endPointRepo: EndPointRepository,
    private val permissionRepo: PermissionRepository,
) {
    fun getList(method: String?, pattern: String?, permissionId: Long?, user: User): List<RecordFilterRule> {
        val userId = user.id ?: return listOf()

        val qRecordFilterRule = QRecordFilterRule.recordFilterRule

        val predicate = BooleanBuilder()
        method?.let { predicate.and(qRecordFilterRule.endPoint.method.eq(it)) }
        pattern?.let { predicate.and(qRecordFilterRule.endPoint.pattern.eq(it)) }
        permissionId?.let { predicate.and(qRecordFilterRule.permission.id.eq(it)) }

        if (userRepo.superAdmin(userId) == true) {
            return queryFactory.selectFrom(qRecordFilterRule).where(predicate).fetch()
        }

        val qGroup = QGroup.group
        val qPermission = QPermission.permission
        val qUser = QUser.user
        val qEndPoint = QEndPoint.endPoint

        predicate.and(qUser.id.eq(user.id))

        val query = queryFactory
            .selectFrom(qRecordFilterRule)
            .leftJoin(qRecordFilterRule.endPoint, qEndPoint)
            .leftJoin(qRecordFilterRule.permission, qPermission)
            .leftJoin(qPermission.groups, qGroup)
            .leftJoin(qGroup.users, qUser)
            .where(predicate)
            .distinct()

        return query.fetch()
    }

    fun create(data: FilterRuleWriteDTO): RecordFilterRule {
        val endPoint = endPointRepo.findByIdOrNull(data.endPointId) ?: throw NotFoundException("未找到 API")
        val permission = permissionRepo.findByIdOrNull(data.permissionId) ?: throw NotFoundException("未找到权限")

        val record = RecordFilterRule(
            name = data.name,
            description = data.description,
            expression = data.expression,
            endPoint = endPoint,
            permission = permission
        )

        return repo.save(record)
    }

    fun delete(id: Long) {
        repo.deleteById(id)
    }
}