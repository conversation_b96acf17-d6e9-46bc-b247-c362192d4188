package com.yanfuinvest.external.portfolioServiceServer.service

import com.yanfuinvest.external.portfolioServiceServer.domain.PortfolioServiceServerResponseCustom
import org.springframework.core.ParameterizedTypeReference
import org.springframework.http.HttpMethod
import org.springframework.stereotype.Service

@Service
class ExternalUserConfigService(
    private val prefix: String = "userConfig"
): PortfolioServiceServerServiceBase<Any>() {
    private val responseType = object : ParameterizedTypeReference<PortfolioServiceServerResponseCustom<Any>>() {}

    fun getByUserId(userId: Long): Any? {
        val input = mapOf("userId" to userId)
        val result = request("$prefix.byId", HttpMethod.GET, input, responseType)

        return result?.result?.data
    }

    fun update(data: Map<String, Any?>) {
        request("$prefix.update", HttpMethod.POST, data)
    }
}