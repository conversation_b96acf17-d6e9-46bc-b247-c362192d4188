package com.yanfuinvest.meta.domain

import com.yanfuinvest.common.annotation.NoArg
import java.io.Serializable

@NoArg
class EndPointId(
    val method: String,
    val pattern: String,
): Serializable {
    override fun hashCode(): Int {
        return method.hashCode() xor pattern.hashCode()
    }

    override fun equals(other: Any?): Boolean {
        return hashCode() == other.hashCode()
    }
}
