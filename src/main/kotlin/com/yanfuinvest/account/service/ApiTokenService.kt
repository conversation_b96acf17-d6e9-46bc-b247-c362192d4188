package com.yanfuinvest.account.service

import com.yanfuinvest.account.domain.ApiToken
import com.yanfuinvest.account.domain.User
import com.yanfuinvest.account.domain.dto.ApiTokenReadDTO
import com.yanfuinvest.account.repository.ApiTokenRepository
import com.yanfuinvest.common.utils.ApiTokenUtil
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class ApiTokenService(
    private val repo: ApiTokenRepository,
    private val util: ApiTokenUtil,
) {
    fun getList(user: User): List<ApiTokenReadDTO> {
        val result = repo.getAllByUserId(user.id!!)
        return result.map { ApiTokenReadDTO(it) }
    }

    @Transactional
    fun create(user: User): Map<String, String> {
        val token = util.generate()
        val tokenHash = util.hashToken(token)
        val tokenMask = token.slice(0 until 6) + "..."

        repo.save(
            ApiToken(
                userId = user.id!!,
                tokenHash = tokenHash,
                tokenMask = tokenMask,
            )
        )

        return mapOf("token" to token)
    }

    @Transactional
    fun delete(user: User, id: Long) {
        repo.deleteByUserIdAndId(user.id!!, id)
    }
}