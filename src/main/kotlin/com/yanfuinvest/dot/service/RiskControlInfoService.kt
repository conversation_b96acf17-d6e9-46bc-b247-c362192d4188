package com.yanfuinvest.dot.service

import com.querydsl.jpa.impl.JPAQueryFactory
import com.yanfuinvest.common.domain.exception.InvalidParamsException
import com.yanfuinvest.dot.domain.QRiskControlInfo
import com.yanfuinvest.dot.domain.RiskControlInfo
import com.yanfuinvest.dot.domain.dto.RiskControlInfoWriteDTO
import com.yanfuinvest.dot.repository.RiskControlInfoRepository
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID
import javax.persistence.LockModeType

@Service
class RiskControlInfoService(
    private val repo: RiskControlInfoRepository,
    private val queryFactory: JPAQueryFactory,
) {
    private val q = QRiskControlInfo.riskControlInfo

    fun getById(id: Int) = repo.findByIdOrNull(id)

    fun getByPfCode(pfCode: String) = repo.findByPfCode(pfCode)

    private fun getForUpdate(pfCode: String): RiskControlInfo? {
        val query = queryFactory.selectFrom(q).where(q.pfCode.eq(pfCode)).setLockMode(LockModeType.PESSIMISTIC_WRITE)
        return query.fetchOne()
    }

    @Transactional
    fun write(data: RiskControlInfoWriteDTO) {
        var record = getForUpdate(data.pfCode)
        if (record == null) {
            record = RiskControlInfo(
                pfCode = data.pfCode,
                shortExposure = data.shortExposure,
                longExposure = data.longExposure,
                stockPercentage = data.stockPercentage,
                maxStockPercentage = data.maxStockPercentage,
                extra = data.extra,
            )
        } else {
            record.shortExposure = data.shortExposure
            record.longExposure = data.longExposure
            record.stockPercentage = data.stockPercentage
            record.maxStockPercentage = data.maxStockPercentage
            record.extra = data.extra
        }

        repo.save(record)
    }

    @Transactional
    fun delete(id: Int) {
        repo.deleteById(id)
    }
}