package com.yanfuinvest.external.portfolioServiceServer.service

import com.yanfuinvest.external.portfolioServiceServer.domain.PortfolioServiceServerResponseCustom
import org.springframework.core.ParameterizedTypeReference
import org.springframework.http.HttpMethod
import org.springframework.stereotype.Service

@Service
class AppropriatenessBaseInfoService(
    private val prefix: String = "baseInfo"
): PortfolioServiceServerServiceBase<Any>() {
    private val responseType = object : ParameterizedTypeReference<PortfolioServiceServerResponseCustom<Any>>() {}

    fun getByUserId(userId: Int): Any? {
        val input = mapOf("userId" to userId)
        val result = request("$prefix.byUserId", HttpMethod.GET, input, responseType)
        return result?.result?.data
    }

    fun getByUserIds(userIds: MutableList<Int>): Any? {
        val input = mapOf("userIds" to userIds)
        val result = request("$prefix.byUserIds", HttpMethod.GET, input, responseType)
        return result?.result?.data
    }
}