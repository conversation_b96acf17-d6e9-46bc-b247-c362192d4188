package com.yanfuinvest.dot.utils

import com.yanfuinvest.dot.domain.enums.AccountInfoTypeEnum
import javax.persistence.AttributeConverter
import javax.persistence.Converter

@Converter
class AccountInfoTypeConverter: AttributeConverter<AccountInfoTypeEnum, String> {
    override fun convertToDatabaseColumn(attribute: AccountInfoTypeEnum?): String? {
        return attribute?.value
    }

    override fun convertToEntityAttribute(data: String?): AccountInfoTypeEnum? {
        return AccountInfoTypeEnum.values().find { it.value == data }
    }
}