package com.yanfuinvest.external.portfolioServiceServer.controller

import com.yanfuinvest.common.domain.http.CustomResponse
import com.yanfuinvest.external.portfolioServiceServer.service.VideoConfigWordsService
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@Validated
@RestController
@RequestMapping("/external/portfolio_service_server/video_config_words")
class VideoConfigWordsController(
    private val service: VideoConfigWordsService
) {
    @GetMapping("")
    fun getList() = CustomResponse.Ok(service.getList())

    @PostMapping("")
    fun create(@RequestBody data: Map<String, Any?>) = CustomResponse.Ok(service.create(data))

    @PutMapping("")
    fun update(@RequestBody data: Map<String, Any?>) = CustomResponse.Ok(service.update(data))
}