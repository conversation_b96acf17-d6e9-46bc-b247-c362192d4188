package com.yanfuinvest.external.portfolioServiceServer.service

import com.yanfuinvest.common.domain.PaginationRequest
import com.yanfuinvest.external.portfolioServiceServer.utils.PortfolioServiceServerUtils
import org.springframework.stereotype.Service

@Service
class SupplementCheckLogService(
    private val prefix: String = "supplementCheckLog",
): PortfolioServiceServerServiceBase<Any>() {
    override val getListPath = "$prefix.list"

    fun getList(
        paginationRequest: PaginationRequest,
        filter: Map<String, Any?>?
    ): Pair<List<Any>, Long> {
        val paginationInput = PortfolioServiceServerUtils.parsePagination(paginationRequest)
        val input = paginationInput.plus(filter ?: emptyMap())

        return getList(input)
    }
}