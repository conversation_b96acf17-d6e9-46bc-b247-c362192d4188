package com.yanfuinvest.common.configuration

import org.springframework.context.ApplicationContext
import org.springframework.context.ApplicationContextAware
import org.springframework.stereotype.Component

@Component
class SpringContext: ApplicationContextAware {
    override fun setApplicationContext(applicationContext: ApplicationContext) {
        setContext(applicationContext)
    }

    companion object {
        private lateinit var context: ApplicationContext

        private fun setContext(applicationContext: ApplicationContext) {
            context = applicationContext
        }

        fun getApplicationContext(): ApplicationContext {
            return context
        }
    }
}