package com.yanfuinvest.external.portfolioServiceServer.service

import org.springframework.http.HttpMethod
import org.springframework.stereotype.Service

@Service
class VideoConfigService(
    private val prefix: String = "videoConfig"
): PortfolioServiceServerServiceBase<Any>() {
    override val getListPath = "$prefix.list"

    fun getVideoList(targetCode: String): Any? {
        val input = mapOf("targetCode" to targetCode)
        return request("$prefix.videoList", HttpMethod.GET, input)
    }
}