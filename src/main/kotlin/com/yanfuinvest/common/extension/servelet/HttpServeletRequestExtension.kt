package com.yanfuinvest.common.extension.servelet

import com.yanfuinvest.common.configuration.SpringContext
import com.yanfuinvest.meta.domain.EndPointId
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.method.HandlerMethod
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping
import org.springframework.web.util.UriComponentsBuilder
import javax.servlet.http.HttpServletRequest

fun HttpServletRequest.getEndPointId(): EndPointId? {
    if (requestURI.startsWith("/swagger") || requestURI == "/v2/api-docs") {
        return EndPointId(method, requestURI)
    }

    val context = SpringContext.getApplicationContext()
    val requestMappingHandlerMapping = context.getBean(RequestMappingHandlerMapping::class.java)

    val handler = requestMappingHandlerMapping.getHandler(this)?.handler ?: return null
    if (handler !is HandlerMethod) return null

    val classRequestMapping = handler.beanType.getAnnotation(RequestMapping::class.java)
    val methodRequestMapping = handler.getMethodAnnotation(RequestMapping::class.java) ?: return null

    val method = methodRequestMapping.method.firstOrNull()?.name ?: return null
    val pathOfClass = classRequestMapping.value.firstOrNull() ?: return null
    val pathOfMethod = methodRequestMapping.value.firstOrNull() ?: return null

    val uriBuilder = UriComponentsBuilder.newInstance()

    if (pathOfClass.isNotEmpty() && pathOfClass != "/") uriBuilder.path("/").path(pathOfClass)
    if (pathOfMethod.isNotEmpty() && pathOfMethod != "/") uriBuilder.path("/").path(pathOfMethod)

    val path = uriBuilder.build().path ?: return null

    return EndPointId(method, path)
}