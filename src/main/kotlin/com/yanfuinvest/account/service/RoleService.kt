package com.yanfuinvest.account.service

import com.yanfuinvest.account.domain.Role
import com.yanfuinvest.account.domain.dto.RoleEditingDTO
import com.yanfuinvest.account.repository.RoleRepository
import com.yanfuinvest.common.domain.exception.InvalidParamsException
import com.yanfuinvest.common.domain.exception.NotFoundException
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class RoleService (
    private val roleRepo: RoleRepository
) {
    @Transactional(readOnly = true)
    fun getList(): List<Role> = roleRepo.findAll()

    @Transactional
    fun create(role: RoleEditingDTO): Role {
        if (roleRepo.existsByName(role.name)) {
            throw InvalidParamsException("角色名重复")
        }
        val savedRole = role.toRole()
        roleRepo.saveAndFlush(savedRole)
        return savedRole
    }

    @Transactional
    fun update(id: Long, role: RoleEditingDTO): Role {
        if (id == 1L) {
            throw InvalidParamsException("禁止修改角色admin")
        }
        val oldRole = roleRepo.findByIdOrNull(id) ?: throw NotFoundException("角色不存在")
        if (!role.name.equals(oldRole.name) && roleRepo.existsByName(role.name)) {
            throw InvalidParamsException("角色名重复")
        }
        RoleEditingDTO.copier.copy(role, oldRole, null)
        roleRepo.saveAndFlush(oldRole)
        return oldRole
    }

    @Transactional
    fun delete(id: Long) {
        if (id == 1L) {
            throw InvalidParamsException("禁止删除角色admin")
        }
        try {
            roleRepo.deleteById(id)
        } catch (e: IllegalArgumentException) {
            throw NotFoundException("角色不存在")
        }
    }
}
