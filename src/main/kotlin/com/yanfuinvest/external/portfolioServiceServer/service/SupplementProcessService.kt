package com.yanfuinvest.external.portfolioServiceServer.service

import com.yanfuinvest.account.domain.User
import com.yanfuinvest.common.domain.PaginationRequest
import com.yanfuinvest.external.portfolioServiceServer.domain.PortfolioServiceServerResponseCustom
import com.yanfuinvest.external.portfolioServiceServer.utils.PortfolioServiceServerUtils
import org.springframework.core.ParameterizedTypeReference
import org.springframework.http.HttpMethod
import org.springframework.stereotype.Service

@Service
class SupplementProcessService(
    private val prefix: String = "supplementProcess"
) : PortfolioServiceServerServiceBase<Any>() {
    override val getListPath = "$prefix.list"

    private val responseType = object : ParameterizedTypeReference<PortfolioServiceServerResponseCustom<Any>>() {}

    fun getList(
        paginationRequest: PaginationRequest,
        filter: Map<String, Any?>?
    ): Pair<List<Any>, Long> {
        val paginationInput = PortfolioServiceServerUtils.parsePagination(paginationRequest)
        val input = paginationInput.plus(filter ?: emptyMap())

        return getList(input)
    }

    fun getSupplementLinksById(id: Int): Any? {
        val input = mapOf("id" to id)
        val result = request("$prefix.supplementLinksByIdPrivate", HttpMethod.GET, input, responseType)

        return result?.result?.data
    }

    fun getDocumentsById(id: Int): Any? {
        val input = mapOf("id" to id)
        return request("$prefix.documentsById", HttpMethod.GET, input)
    }

    fun create(user: User, data: Map<String, Any?>) {
        val input = getOperatorMap(user).plus(data)
        request("$prefix.create", HttpMethod.POST, input)
    }

    fun review(user: User, path: String, data: Map<String, Any?>) {
        val input = getOperatorMap(user).plus(data)
        request("$prefix.$path", HttpMethod.POST, input)
    }

    fun getLogList(): Any? {
        val result = request("$prefix.logList", HttpMethod.GET, null, responseType)
        return result?.result?.data
    }

    fun terminate(user: User, data: Map<String, Any?>) {
        val input = getOperatorMap(user).plus(data)
        request("$prefix.terminate", HttpMethod.POST, input)
    }

    private fun getOperatorMap(user: User): Map<String, Long?> {
        return mapOf("operatorId" to user.id)
    }
}