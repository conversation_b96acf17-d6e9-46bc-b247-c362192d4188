package com.yanfuinvest.external.portfolioServiceServer.controller

import com.yanfuinvest.account.domain.User
import com.yanfuinvest.common.domain.PaginationRequest
import com.yanfuinvest.common.domain.http.CustomResponse
import com.yanfuinvest.external.portfolioServiceServer.service.RiskTestProcessService
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*

@Validated
@RestController
@RequestMapping("/external/portfolio_service_server/risk_test_process")
class RiskTestProcessController(
    private val service: RiskTestProcessService,
) {
    @PostMapping("list")
    fun getList(
        @RequestParam page: Int,
        @RequestParam pageSize: Int,
        @RequestBody(required = false) filter: Map<String, Any?>?,
    ): CustomResponse<List<Any>> {
        val (result, amount) = service.getList(PaginationRequest(page, pageSize), filter)
        return CustomResponse.Ok(data = result, amount = amount)
    }

    @GetMapping("{id}")
    fun getById(@PathVariable id: Int) = CustomResponse.Ok(service.getById(id))

    @GetMapping("questionnaire_link/{id}")
    fun getQuestionnaireById(@PathVariable id: Int) = CustomResponse.Ok(service.getQuestionnaireById(id))

    @PostMapping("create")
    fun create(@AuthenticationPrincipal user: User, @RequestBody data: Map<String, Any?>) =
        CustomResponse.Ok(service.create(user, data))
}