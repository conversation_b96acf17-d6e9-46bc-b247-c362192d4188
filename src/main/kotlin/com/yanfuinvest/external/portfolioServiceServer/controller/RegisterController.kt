package com.yanfuinvest.external.portfolioServiceServer.controller

import com.yanfuinvest.common.domain.PaginationRequest
import com.yanfuinvest.common.domain.http.CustomResponse
import com.yanfuinvest.external.portfolioServiceServer.service.RegisterService
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*

@Validated
@RestController
@RequestMapping("/external/portfolio_service_server/register")
class RegisterController(private val service: RegisterService) {
    @PostMapping("list")
    fun getList(
        @RequestParam page: Int,
        @RequestParam pageSize: Int,
        @RequestBody(required = false) filter: Map<String, Any?>?
    ): CustomResponse<List<Any>> {
        val (result, amount) = service.getList(
            PaginationRequest(page, pageSize),
            filter,
        )
        return CustomResponse.Ok(data = result, amount = amount)
    }

    @PostMapping("generate_code/{id}")
    fun generateCode(@PathVariable id: Int) = CustomResponse.Ok(service.generateCode(id))

    @PostMapping("redo_identity_verification/{id}")
    fun redoIdentityVerification(@PathVariable id: Int) = CustomResponse.Ok(service.redoIdentityVerification(id))
}