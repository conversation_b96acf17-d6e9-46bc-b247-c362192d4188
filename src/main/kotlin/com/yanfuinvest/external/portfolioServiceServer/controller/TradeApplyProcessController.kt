package com.yanfuinvest.external.portfolioServiceServer.controller

import com.yanfuinvest.account.domain.User
import com.yanfuinvest.common.domain.http.CustomResponse
import com.yanfuinvest.external.portfolioServiceServer.service.PortfolioServiceTradeApplyProcessService
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*

@Validated
@RestController
@RequestMapping("/external/portfolio_service_server/trade_apply_process")
class TradeApplyProcessController(
    private val service: PortfolioServiceTradeApplyProcessService
) {
    @GetMapping("list/{identityNumber}")
    fun getListByIdentityNumber(@PathVariable identityNumber: String) =
        CustomResponse.Ok(service.getListByIdentityNumber(identityNumber))

    @GetMapping("documents/{id}")
    fun getDocumentsById(@PathVariable id: Int) = CustomResponse.Ok(service.getDocumentsById(id))

    @PostMapping("create")
    fun create(@AuthenticationPrincipal user: User, @RequestBody data: Map<String, Any?>) =
        CustomResponse.Ok(service.create(user, data))

    @PostMapping("review/{path}")
    fun review(
        @AuthenticationPrincipal user: User,
        @PathVariable("path") path: String,
        @RequestBody data: Map<String, Any?>,
    ) = CustomResponse.Ok(service.review(user, path, data))
}