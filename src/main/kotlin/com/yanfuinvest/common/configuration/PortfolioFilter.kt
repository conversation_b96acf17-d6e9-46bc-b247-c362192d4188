package com.yanfuinvest.common.configuration

import org.aspectj.lang.JoinPoint
import org.aspectj.lang.annotation.After
import org.aspectj.lang.annotation.Aspect
import org.aspectj.lang.annotation.Before
import org.aspectj.lang.annotation.Pointcut
import org.hibernate.Session
import org.springframework.stereotype.Component
import javax.persistence.EntityManager

@Aspect
@Component
class PortfolioFilter(private val entityManager: EntityManager) {
    @Pointcut("@within(org.springframework.stereotype.Service)")
    fun servicePointcut() {}

    @Pointcut("within(com.yanfuinvest.operation.repository.PortfolioRepository)")
    fun portfolioRepoPointcut() {}

    @Pointcut("execution(* springfox..*.*(..))")
    fun springfoxPointcut() {}

    @Pointcut("@within(com.yanfuinvest.common.annotation.PortfolioFilterIgnore)")
    fun portfolioFilterIgnorePointcut() {}

    @Before("(servicePointcut() || portfolioRepoPointcut()) && !springfoxPointcut() && !portfolioFilterIgnorePointcut()")
    fun beforeService(joinPoint: JoinPoint) {
        val session = entityManager.unwrap(Session::class.java)
        session.enableFilter("portfolioFilter")
    }

    @After("(servicePointcut() || portfolioRepoPointcut()) && !springfoxPointcut() && !portfolioFilterIgnorePointcut()")
    fun afterService(joinPoint: JoinPoint) {
        val session = entityManager.unwrap(Session::class.java)
        session.disableFilter("portfolioFilter")
    }
}