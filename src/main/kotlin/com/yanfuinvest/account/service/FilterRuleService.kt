package com.yanfuinvest.account.service

import com.querydsl.core.BooleanBuilder
import com.querydsl.jpa.impl.JPAQueryFactory
import com.yanfuinvest.account.domain.*
import com.yanfuinvest.account.domain.dto.FilterRuleWriteDTO
import com.yanfuinvest.account.repository.FilterRuleRepository
import com.yanfuinvest.account.repository.PermissionRepository
import com.yanfuinvest.account.repository.UserRepository
import com.yanfuinvest.meta.domain.EndPointId
import com.yanfuinvest.meta.domain.QEndPoint
import com.yanfuinvest.meta.repository.EndPointRepository
import javassist.NotFoundException
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service

@Service
class FilterRuleService(
    private val repo: FilterRuleRepository,
    private val queryFactory: JPAQueryFactory,
    private val userRepo: UserRepository,
    private val endPointRepo: EndPointRepository,
    private val permissionRepo: PermissionRepository,
) {
    fun getList(endPointId: EndPointId?, userId: Long?): List<FilterRule> {
        if (endPointId == null || userId == null || userRepo.superAdmin(userId) == true) return listOf()

        val qFilterRule = QFilterRule.filterRule
        val qGroup = QGroup.group
        val qPermission = QPermission.permission
        val qUser = QUser.user
        val qEndPoint = QEndPoint.endPoint

        val predicate = qUser.id.eq(userId)
            .and(qEndPoint.method.eq(endPointId.method).and(qEndPoint.pattern.eq(endPointId.pattern)))

        val query = queryFactory
            .selectFrom(qFilterRule)
            .leftJoin(qFilterRule.endPoint, qEndPoint)
            .leftJoin(qFilterRule.permission, qPermission)
            .leftJoin(qPermission.groups, qGroup)
            .leftJoin(qGroup.users, qUser)
            .where(predicate)
            .distinct()

        return query.fetch()
    }

    fun getList(method: String?, pattern: String?, permissionId: Long?, user: User): List<FilterRule> {
        val userId = user.id ?: return listOf()

        val qFilterRule = QFilterRule.filterRule

        val predicate = BooleanBuilder()
        method?.let { predicate.and(qFilterRule.endPoint.method.eq(it)) }
        pattern?.let { predicate.and(qFilterRule.endPoint.pattern.eq(it)) }
        permissionId?.let { predicate.and(qFilterRule.permission.id.eq(it)) }

        if (userRepo.superAdmin(userId) == true) {
            return queryFactory.selectFrom(qFilterRule).where(predicate).fetch()
        }


        val qGroup = QGroup.group
        val qPermission = QPermission.permission
        val qUser = QUser.user
        val qEndPoint = QEndPoint.endPoint

        predicate.and(qUser.id.eq(user.id))

        val query = queryFactory
            .selectFrom(qFilterRule)
            .leftJoin(qFilterRule.endPoint, qEndPoint)
            .leftJoin(qFilterRule.permission, qPermission)
            .leftJoin(qPermission.groups, qGroup)
            .leftJoin(qGroup.users, qUser)
            .where(predicate)
            .distinct()

        return query.fetch()
    }

    fun create(data: FilterRuleWriteDTO): FilterRule {
        val endPoint = endPointRepo.findByIdOrNull(data.endPointId) ?: throw NotFoundException("未找到 API")
        val permission = permissionRepo.findByIdOrNull(data.permissionId) ?: throw NotFoundException("未找到权限")

        val record = FilterRule(
            name = data.name,
            description = data.description,
            expression = data.expression,
            endPoint = endPoint,
            permission = permission
        )

        return repo.save(record)
    }

    fun delete(id: Long) {
        repo.deleteById(id)
    }
}