package com.yanfuinvest.common.utils.jpaConvertor

import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.databind.ObjectMapper
import java.io.IOException
import javax.persistence.AttributeConverter

class JpaConvertorJson: AttributeConverter<Any, String> {
    private val objectMapper = ObjectMapper()

    override fun convertToDatabaseColumn(attribute: Any?): String? {
        return try {
            objectMapper.writeValueAsString(attribute)
        } catch (e: Exception) {
            null
        }
    }

    override fun convertToEntityAttribute(dbData: String?): Any? {
        return try {
            objectMapper.readValue(dbData, Object::class.java)
        } catch (e: Exception) {
            if (dbData != null) {
                print(e)
            }
            null
        }
    }
}
