package com.yanfuinvest.common.extension.time

import java.time.LocalDate

fun LocalDate.withEndOfMonth(): LocalDate {
    return plusMonths(1).withDayOfMonth(1).minusDays(1)
}

fun LocalDate.withEndOfQuarter(): LocalDate {
    val quarter = (monthValue - 1) / 3 + 1
    return withMonth(quarter * 3).withEndOfMonth()
}

fun LocalDate.withEndOfYear(): LocalDate {
    return plusYears(1).withMonth(1).withDayOfMonth(1).minusDays(1)
}
