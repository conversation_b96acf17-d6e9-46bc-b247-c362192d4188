package com.yanfuinvest.dot.controller

import com.yanfuinvest.common.domain.http.CustomResponse
import com.yanfuinvest.dot.domain.dto.RiskControlInfoWriteDTO
import com.yanfuinvest.dot.service.RiskControlInfoService
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@Validated
@RestController
@RequestMapping("/dot/risk_control_info")
class RiskControlInfoController(
    private val service: RiskControlInfoService,
) {
    @GetMapping("")
    fun getByPfCode(@RequestParam pfCode: String) = CustomResponse.Ok(service.getByPfCode(pfCode))

    @PostMapping("")
    fun write(@RequestBody data: RiskControlInfoWriteDTO) = CustomResponse.Ok(service.write(data))
}