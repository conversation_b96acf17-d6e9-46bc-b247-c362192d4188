package com.yanfuinvest.common.configuration

import com.yanfuinvest.account.service.UserService
import com.yanfuinvest.common.domain.constant.Perms
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.HttpMethod
import org.springframework.http.client.ClientHttpRequestFactory
import org.springframework.security.authentication.AuthenticationManager
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter
import org.springframework.security.config.http.SessionCreationPolicy
import org.springframework.security.web.session.HttpSessionEventPublisher
import org.springframework.security.web.util.matcher.RequestMatcher
import org.springframework.web.client.RestTemplate
import javax.servlet.http.HttpServletResponse

@Configuration
@EnableWebSecurity
//@EnableGlobalMethodSecurity(prePostEnabled = true)
class SecurityConf(
    private val userService: UserService,
    private val providers: Providers
): WebSecurityConfigurerAdapter() {
    @Bean
    override fun authenticationManagerBean(): AuthenticationManager {
        return super.authenticationManagerBean()
    }

    @Bean
    fun httpSessionEventPublisher(): HttpSessionEventPublisher {
        return HttpSessionEventPublisher()
    }

    @Bean
    fun restTemplate() =
        RestTemplate()

    override fun configure(auth: AuthenticationManagerBuilder) {
        auth.userDetailsService(userService).passwordEncoder(providers.pwdEncoder)
    }

    override fun configure(http: HttpSecurity) {
        http.headers().frameOptions().sameOrigin()

        http
            .csrf().disable()
            .formLogin().disable()
            .httpBasic().disable()
            .logout().disable()
            .sessionManagement()
            .sessionCreationPolicy(SessionCreationPolicy.IF_REQUIRED)
            .and()
            .exceptionHandling()
            .authenticationEntryPoint { request, response, e ->
                response.status = HttpServletResponse.SC_UNAUTHORIZED
            }
            .accessDeniedHandler { request, response, e ->
                response.status = HttpServletResponse.SC_FORBIDDEN
            }
//            .and()
//            .authorizeRequests()
//            .antMatchers("/jira/webhooks").permitAll()
            // 登录api只能匿名用户访问
//            .antMatchers(HttpMethod.POST, "/account/login").access("isAnonymous()")
//            .antMatchers(HttpMethod.POST, "/account/login_feishu").access("isAnonymous()")
//            // account下的api，诸如修改密码等，只要登录用户都能访问
//            .antMatchers("/account/**").access("!isAnonymous()")
//            // admin下的必须拥有superAdmin权限
//            .antMatchers("/admin/**")
//                .access("hasPermission('gte', @perms.superAdmin) AND hasPermission('pwdAvailable', '')")
//            // 开放日提醒抄送人员维护，允许investor manager访问
//            .antMatchers("/operation/carbon_copier")
//                .access("hasPermission('gte', @perms.accountDetail)")
//            // 产品及公共数据的api要求可以被本地访问，用于报表生成的接口
//            .antMatchers(HttpMethod.GET, "/operation/portfolio/**", "/data/**")
//                .access("hasPermission('pwdAvailable', '') OR hasPermission('fromLocal', '')")
//            // 拥有readonlyUser权限的人不允许修改数据
//            .requestMatchers(RequestMatcher {
//                it.method in listOf("POST", "PUT", "PATCH", "DELETE")
//            })
//                .access("hasPermission('pwdAvailable', '') AND NOT hasPermission('gte', @perms.readonlyUser)")
            // 一般的api必须要求密码有效
//            .anyRequest().access("hasPermission('pwdAvailable', '')")
//            .anyRequest().access("!isAnonymous()")
    }
}
