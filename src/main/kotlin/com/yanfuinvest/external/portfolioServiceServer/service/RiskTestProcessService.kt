package com.yanfuinvest.external.portfolioServiceServer.service

import com.yanfuinvest.account.domain.User
import com.yanfuinvest.common.domain.PaginationRequest
import com.yanfuinvest.external.portfolioServiceServer.domain.PortfolioServiceServerResponseCustom
import com.yanfuinvest.external.portfolioServiceServer.utils.PortfolioServiceServerUtils
import org.springframework.core.ParameterizedTypeReference
import org.springframework.http.HttpMethod
import org.springframework.stereotype.Service

@Service
class RiskTestProcessService(
    private val prefix: String = "riskTestProcess",
): PortfolioServiceServerServiceBase<Any>() {
    override val getListPath = "$prefix.list"
    private val responseType = object : ParameterizedTypeReference<PortfolioServiceServerResponseCustom<Any>>() {}

    fun getList(
        paginationRequest: PaginationRequest,
        filter: Map<String, Any?>?,
    ): Pair<List<Any>, Long> {
        val paginationInput = PortfolioServiceServerUtils.parsePagination(paginationRequest)
        val input = paginationInput.plus(filter ?: emptyMap())

        return getList(input)
    }

    fun getById(id: Int): Any? {
        val input = mapOf("id" to id)
        val result = request("$prefix.byId", HttpMethod.GET, input, responseType)
        return result?.result?.data
    }

    fun create(user: User, data: Map<String, Any?>) {
        val input = getOperatorMap(user).plus(data)
        request("$prefix.managerCreate", HttpMethod.POST, input)
    }

    fun getQuestionnaireById(id: Int): Any? {
        val input = mapOf("id" to id)
        val result = request("$prefix.questionnaireLinkById", HttpMethod.GET, input, responseType)
        return result?.result?.data
    }

    private fun getOperatorMap(user: User): Map<String, Long?> {
        return mapOf("operatorId" to user.id)
    }
}