package com.yanfuinvest.common.extension.date

import com.yanfuinvest.common.utils.Utils
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.Date

fun Date.toLocalDate(): LocalDate {
    return java.sql.Date(this.time).toLocalDate()
}

fun Date.toLocalDateTime(): LocalDateTime {
    return java.sql.Timestamp(this.time).toLocalDateTime()
}

fun Date.toDashString(): String {
    return Utils.getDashFormattedDate(this)
}
