package com.yanfuinvest.external.portfolioServiceServer.service

import com.yanfuinvest.account.domain.User
import com.yanfuinvest.account.repository.UserRepository
import com.yanfuinvest.common.domain.PaginationRequest
import com.yanfuinvest.common.domain.exception.InvalidParamsException
import com.yanfuinvest.external.portfolioServiceServer.domain.PortfolioServicePortfolioReport
import com.yanfuinvest.external.portfolioServiceServer.domain.PortfolioServiceServerResponseList
import com.yanfuinvest.external.portfolioServiceServer.domain.dto.PortfolioReportCreateDTO
import com.yanfuinvest.external.portfolioServiceServer.domain.dto.PortfolioReportUpdateDTO
import com.yanfuinvest.external.portfolioServiceServer.utils.PortfolioServiceServerUtils
import com.yanfuinvest.operation.repository.PortfolioRepository
import org.springframework.core.ParameterizedTypeReference
import org.springframework.data.repository.findByIdOrNull
import org.springframework.http.HttpMethod
import org.springframework.stereotype.Service

@Service
class PortfolioServicePortfolioReportService(
    private val userRepo: UserRepository,
    private val portfolioRepo: PortfolioRepository,
): PortfolioServiceServerServiceBase<PortfolioServicePortfolioReport>() {
    private val prefix: String = "portfolioReport"

    override val getListPath = "$prefix.list"
    override val listResponseType = object: ParameterizedTypeReference<PortfolioServiceServerResponseList<PortfolioServicePortfolioReport>>() {}

    fun getList(
        paginationRequest: PaginationRequest,
        portfolioId: Long?,
        date: String?,
        published: Boolean?,
    ): Pair<List<PortfolioServicePortfolioReport>, Long> {
        val paginationInput = PortfolioServiceServerUtils.parsePagination(paginationRequest)
        val filterInput = mapOf(
            "portfolioId" to portfolioId,
            "date" to date,
            "published" to published,
        )
        val input = paginationInput.plus(filterInput)

        val (result, amount) = getList(input)
        val userIds = result.mapNotNull { it.publishedBy }.distinct()
        val userDict = userRepo.findAllById(userIds).associateBy { it.id }

        for (item in result) {
            val user = userDict[item.publishedBy] ?: continue
            item.publishedByName = user.realName ?: user.username
        }

        return Pair(result, amount)
    }

    fun create(user: User, data: PortfolioReportCreateDTO) {
        val portfolioId = data.portfolioId ?: throw InvalidParamsException("请填写产品")

        val portfolioIds = mutableSetOf(portfolioId)
        data.publishedBy = user.id

        if (data.includeClassAB == true) {
            val record = portfolioRepo.findByIdOrNull(portfolioId)
            record?.classAShareId?.let { portfolioIds.add(it) }
            record?.classBShareId?.let { portfolioIds.add(it) }
            record?.classCShareId?.let { portfolioIds.add(it) }
        }

        for (id in portfolioIds) {
            val input = data.withPortfolioId(id)
            request("$prefix.create", HttpMethod.POST, input)
        }
    }

    fun update(user: User, data: PortfolioReportUpdateDTO) {
        data.publishedBy = user.id
        request("$prefix.update", HttpMethod.POST, data)
    }

    fun publish(user: User, data: PortfolioReportUpdateDTO) {
        data.publishedBy = user.id
        request("$prefix.publish", HttpMethod.POST, data)
    }

    fun delete(user: User, data: PortfolioReportUpdateDTO) {
        data.publishedBy = user.id
        request("$prefix.delete", HttpMethod.POST, data)
    }
}